#!/usr/bin/env python3
"""
Demonstration and test script for A2L Signal Search Application

This script demonstrates the functionality of the A2L search application
and provides comprehensive testing capabilities.
"""

import os
import re
import tempfile

# Sample A2L content for testing
SAMPLE_A2L_CONTENT = '''
/* Sample A2L file for testing A2L Signal Search Application */
ASAP2_VERSION 1 41
/begin PROJECT
   TEST_PROJECT
   "Test Project for A2L Signal Search"
   
   /begin MODULE
      TEST_MODULE
      "Test Module"
      
      /begin MEASUREMENT eng_hour_ofs
         "LIF NVRAM, ECU replacement offset for ENG HOUR"
         UWORD
         _CNV_A_R_LINEAR_____0_CM
         1
         100.
         0.
         6553.5
         DISPLAY_IDENTIFIER ENG_HOUR_OFS
         ECU_ADDRESS 0x40005c70
         FORMAT "%6.1"
      /end MEASUREMENT

      /begin MEASUREMENT abc_conv_mon
         "Anti-bounce counter for ADC error"
         UBYTE
         _CNV_A_R_LINEAR_____3_CM
         1
         100.
         0.
         255.
         DISPLAY_IDENTIFIER ABC_CONV_MON
         ECU_ADDRESS 0x40005248
         FORMAT "%3.0"
      /end MEASUREMENT

      /begin MEASUREMENT state_clear_kwp
         "UDS 31h, clear selected NVM group by keyword"
         ULONG
         _CNV_A_R_LINEAR_____3_CM
         1
         100.
         0.
         4294967295.
         DISPLAY_IDENTIFIER STATE_CLEAR_KWP
         ECU_ADDRESS 0x40005440
         FORMAT "%10.0"
      /end MEASUREMENT

      /begin CHARACTERISTIC c_abc_inc_conv_mon
         "Anti bounce counter increment"
         VALUE
         0x80005248
         _REC_S1VAL_20_U1
         255.
         _CNV_A_R_LINEAR_____3_CM
         0.
         255.
         DISPLAY_IDENTIFIER C_ABC_INC_CONV_MON
         FORMAT "%3.0"
      /end CHARACTERISTIC

      /begin CHARACTERISTIC lc_test_flag
         "Test flag for demonstration"
         VALUE
         0x80004eef
         _REC_S1VAL_20_U1
         1.
         _CNV_S_2_RANGE______2_CM
         0.
         1.
         DISPLAY_IDENTIFIER LC_TEST_FLAG
         BIT_MASK 0x1
      /end CHARACTERISTIC

   /end MODULE
/end PROJECT
'''

def create_test_a2l_file():
    """Create a temporary A2L file for testing."""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.a2l', delete=False, encoding='utf-8') as f:
        f.write(SAMPLE_A2L_CONTENT)
        return f.name

def search_signal_demo(signal_name, file_path):
    """Demonstrate signal search functionality."""
    signal_name_lower = signal_name.lower()
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            current_section = None
            current_signal = None
            signal_content = []
            in_target_signal = False
            
            for line in file:
                line = line.strip()
                
                # Check for beginning of MEASUREMENT or CHARACTERISTIC
                measurement_match = re.match(r'/begin\s+MEASUREMENT\s+(\w+)', line, re.IGNORECASE)
                characteristic_match = re.match(r'/begin\s+CHARACTERISTIC\s+(\w+)', line, re.IGNORECASE)
                
                if measurement_match:
                    current_section = 'MEASUREMENT'
                    current_signal = measurement_match.group(1)
                    signal_content = []
                    in_target_signal = (current_signal.lower() == signal_name_lower)
                    continue
                
                elif characteristic_match:
                    current_section = 'CHARACTERISTIC'
                    current_signal = characteristic_match.group(1)
                    signal_content = []
                    in_target_signal = (current_signal.lower() == signal_name_lower)
                    continue
                
                # Check for end of section
                if re.match(r'/end\s+(MEASUREMENT|CHARACTERISTIC)', line, re.IGNORECASE):
                    if in_target_signal and current_signal:
                        # Found our target signal, extract information
                        content_str = '\n'.join(signal_content)
                        return extract_signal_info(current_signal, content_str, current_section)
                    
                    current_section = None
                    current_signal = None
                    signal_content = []
                    in_target_signal = False
                    continue
                
                # Collect content if we're in the target signal
                if in_target_signal:
                    signal_content.append(line)
        
        return None
        
    except Exception as e:
        print(f"Error reading file: {e}")
        return None

def extract_signal_info(signal_name, content, signal_type):
    """Extract signal information from content."""
    info = {
        'name': signal_name,
        'type': signal_type,
        'description': '',
        'data_type': '',
        'characteristic_type': '',
        'address': '',
        'ecu_address': '',
        'display_identifier': '',
        'format': '',
        'bit_mask': '',
        'conversion': '',
        'lower_limit': '',
        'upper_limit': '',
        'raw_content': content
    }
    
    # Extract description (usually the first quoted string)
    desc_match = re.search(r'"([^"]*)"', content)
    if desc_match:
        info['description'] = desc_match.group(1)
    
    if signal_type == 'MEASUREMENT':
        # Extract data type
        data_type_match = re.search(r'(UBYTE|SBYTE|UWORD|SWORD|ULONG|SLONG|FLOAT32_IEEE)', content)
        if data_type_match:
            info['data_type'] = data_type_match.group(1)
        
        # Extract ECU address
        ecu_addr_match = re.search(r'ECU_ADDRESS\s+(0x[0-9a-fA-F]+)', content)
        if ecu_addr_match:
            info['ecu_address'] = ecu_addr_match.group(1)
    
    elif signal_type == 'CHARACTERISTIC':
        # Extract characteristic type
        char_type_match = re.search(r'(VALUE|CURVE|MAP|CUBOID|CUBE_4|CUBE_5)', content)
        if char_type_match:
            info['characteristic_type'] = char_type_match.group(1)
        
        # Extract address (hex value)
        addr_match = re.search(r'0x[0-9a-fA-F]+', content)
        if addr_match:
            info['address'] = addr_match.group(0)
        
        # Extract bit mask if present
        bit_mask_match = re.search(r'BIT_MASK\s+(0x[0-9a-fA-F]+)', content)
        if bit_mask_match:
            info['bit_mask'] = bit_mask_match.group(1)
    
    # Extract common fields
    display_id_match = re.search(r'DISPLAY_IDENTIFIER\s+(\w+)', content)
    if display_id_match:
        info['display_identifier'] = display_id_match.group(1)
    
    format_match = re.search(r'FORMAT\s+"([^"]*)"', content)
    if format_match:
        info['format'] = format_match.group(1)
    
    conv_match = re.search(r'_CNV_[A-Z0-9_]+', content)
    if conv_match:
        info['conversion'] = conv_match.group(0)
    
    # Extract numeric values
    numbers = re.findall(r'\b\d+\.?\d*\b', content)
    if len(numbers) >= 3:
        info['lower_limit'] = numbers[2] if len(numbers) > 2 else ''
        info['upper_limit'] = numbers[3] if len(numbers) > 3 else ''
    
    return info

def print_signal_details_demo(signal_info):
    """Print detailed information about a signal."""
    print("\n" + "="*80)
    print(f"SIGNAL DETAILS: {signal_info['name']}")
    print("="*80)
    
    print(f"Type: {signal_info['type']}")
    print(f"Description: {signal_info['description']}")
    
    if signal_info['type'] == 'MEASUREMENT':
        if signal_info['data_type']:
            print(f"Data Type: {signal_info['data_type']}")
        if signal_info['ecu_address']:
            print(f"ECU Address: {signal_info['ecu_address']}")
    
    elif signal_info['type'] == 'CHARACTERISTIC':
        if signal_info['characteristic_type']:
            print(f"Characteristic Type: {signal_info['characteristic_type']}")
        if signal_info['address']:
            print(f"Address: {signal_info['address']}")
        if signal_info['bit_mask']:
            print(f"Bit Mask: {signal_info['bit_mask']}")
    
    if signal_info['display_identifier']:
        print(f"Display Identifier: {signal_info['display_identifier']}")
    
    if signal_info['conversion']:
        print(f"Conversion Method: {signal_info['conversion']}")
    
    if signal_info['format']:
        print(f"Format: {signal_info['format']}")
    
    if signal_info['lower_limit'] and signal_info['upper_limit']:
        print(f"Range: {signal_info['lower_limit']} to {signal_info['upper_limit']}")
    
    print("="*80)

def run_comprehensive_test():
    """Run comprehensive test of the A2L search functionality."""
    print("A2L Signal Search Application - Comprehensive Test")
    print("="*60)
    
    # Create test file
    print("Creating test A2L file...")
    test_file = create_test_a2l_file()
    print(f"Test file created: {test_file}")
    
    try:
        # Test cases
        test_cases = [
            ('eng_hour_ofs', 'MEASUREMENT signal with UWORD data type'),
            ('ENG_HOUR_OFS', 'Case insensitive search (uppercase)'),
            ('abc_conv_mon', 'MEASUREMENT signal with UBYTE data type'),
            ('state_clear_kwp', 'MEASUREMENT signal with ULONG data type'),
            ('c_abc_inc_conv_mon', 'CHARACTERISTIC signal with VALUE type'),
            ('lc_test_flag', 'CHARACTERISTIC signal with BIT_MASK'),
            ('LC_TEST_FLAG', 'Case insensitive CHARACTERISTIC search'),
            ('nonexistent_signal', 'Signal that does not exist')
        ]
        
        print(f"\nRunning {len(test_cases)} test cases...")
        print("-" * 60)
        
        passed = 0
        failed = 0
        
        for signal_name, description in test_cases:
            print(f"\nTest: {description}")
            print(f"Searching for: '{signal_name}'")
            
            result = search_signal_demo(signal_name, test_file)
            
            if signal_name.lower() == 'nonexistent_signal':
                # This should fail
                if result is None:
                    print("✓ PASS - Signal correctly not found")
                    passed += 1
                else:
                    print("✗ FAIL - Signal should not have been found")
                    failed += 1
            else:
                # This should succeed
                if result:
                    print(f"✓ PASS - Found {result['type']} signal: {result['name']}")
                    print(f"  Description: {result['description']}")
                    passed += 1
                else:
                    print("✗ FAIL - Signal should have been found")
                    failed += 1
        
        print("\n" + "="*60)
        print("TEST SUMMARY")
        print("="*60)
        print(f"Total tests: {len(test_cases)}")
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print(f"Success rate: {(passed/len(test_cases)*100):.1f}%")
        
        if failed == 0:
            print("\n🎉 ALL TESTS PASSED! The A2L search application is working correctly.")
        else:
            print(f"\n⚠️  {failed} test(s) failed. Please check the implementation.")
        
        # Demonstrate detailed output
        print("\n" + "="*60)
        print("DETAILED OUTPUT DEMONSTRATION")
        print("="*60)
        
        demo_signal = 'eng_hour_ofs'
        result = search_signal_demo(demo_signal, test_file)
        if result:
            print_signal_details_demo(result)
        
        print("\n" + "="*60)
        print("USAGE INSTRUCTIONS")
        print("="*60)
        print("To use the A2L Signal Search Application:")
        print("")
        print("1. Command line with signal name:")
        print("   python main.py signal_name")
        print("")
        print("2. Command line with signal name and file:")
        print("   python main.py signal_name path/to/file.a2l")
        print("")
        print("3. Interactive mode:")
        print("   python main.py")
        print("   (then follow the prompts)")
        print("")
        print("Examples:")
        print("   python main.py eng_hour_ofs")
        print("   python main.py ABC_CONV_MON")
        print("   python main.py state_clear_kwp FS_RNCP3_3b_B40_4veh.a2l")
        
    finally:
        # Clean up
        if os.path.exists(test_file):
            os.unlink(test_file)
            print(f"\nCleaned up test file: {test_file}")

if __name__ == "__main__":
    run_comprehensive_test()
