#!/usr/bin/env python3
"""
Debug script to help identify why LDP_TCO__IP_IGA_ST is not being found

This script will:
1. Search for the exact signal name in the A2L file
2. Search for partial matches
3. Show the actual content around potential matches
4. Test different variations of the signal name
"""

import re
import os

def debug_signal_search(signal_name, a2l_file_path):
    """Debug why a signal is not being found."""
    print(f"Debugging Signal Search for: {signal_name}")
    print(f"A2L File: {a2l_file_path}")
    print("=" * 80)
    
    if not os.path.exists(a2l_file_path):
        print(f"❌ ERROR: A2L file '{a2l_file_path}' not found!")
        return
    
    try:
        with open(a2l_file_path, 'r', encoding='utf-8', errors='ignore') as file:
            content = file.read()
        
        print(f"✅ File loaded successfully ({len(content)} characters)")
        print()
        
        # 1. Search for exact signal name anywhere in the file
        print("1. SEARCHING FOR EXACT SIGNAL NAME")
        print("-" * 40)
        
        exact_matches = []
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if signal_name.lower() in line.lower():
                exact_matches.append((i, line.strip()))
        
        if exact_matches:
            print(f"Found {len(exact_matches)} line(s) containing '{signal_name}':")
            for line_num, line_content in exact_matches[:10]:  # Show first 10 matches
                print(f"  Line {line_num}: {line_content}")
        else:
            print(f"❌ No lines found containing '{signal_name}'")
        
        print()
        
        # 2. Search for partial matches
        print("2. SEARCHING FOR PARTIAL MATCHES")
        print("-" * 40)
        
        # Try different parts of the signal name
        signal_parts = signal_name.split('_')
        for part in signal_parts:
            if len(part) >= 3:  # Only search for parts with 3+ characters
                partial_matches = []
                for i, line in enumerate(lines, 1):
                    if part.lower() in line.lower() and ('begin' in line.lower() or 'measurement' in line.lower() or 'characteristic' in line.lower()):
                        partial_matches.append((i, line.strip()))
                
                if partial_matches:
                    print(f"Found {len(partial_matches)} line(s) containing '{part}':")
                    for line_num, line_content in partial_matches[:5]:  # Show first 5 matches
                        print(f"  Line {line_num}: {line_content}")
                    print()
        
        # 3. Search for MEASUREMENT and CHARACTERISTIC blocks
        print("3. SEARCHING FOR MEASUREMENT/CHARACTERISTIC BLOCKS")
        print("-" * 50)
        
        measurement_pattern = r'/begin\s+MEASUREMENT\s+([A-Za-z0-9_]+)'
        characteristic_pattern = r'/begin\s+CHARACTERISTIC\s+([A-Za-z0-9_]+)'
        
        measurement_signals = []
        characteristic_signals = []
        
        for i, line in enumerate(lines, 1):
            measurement_match = re.match(measurement_pattern, line.strip(), re.IGNORECASE)
            if measurement_match:
                measurement_signals.append((i, measurement_match.group(1)))
            
            characteristic_match = re.match(characteristic_pattern, line.strip(), re.IGNORECASE)
            if characteristic_match:
                characteristic_signals.append((i, characteristic_match.group(1)))
        
        print(f"Found {len(measurement_signals)} MEASUREMENT signals")
        print(f"Found {len(characteristic_signals)} CHARACTERISTIC signals")
        print()
        
        # 4. Look for similar signal names
        print("4. SEARCHING FOR SIMILAR SIGNAL NAMES")
        print("-" * 40)
        
        all_signals = [sig for _, sig in measurement_signals] + [sig for _, sig in characteristic_signals]
        similar_signals = []
        
        signal_lower = signal_name.lower()
        for sig in all_signals:
            sig_lower = sig.lower()
            # Check for partial matches or similar patterns
            if (signal_lower in sig_lower or 
                sig_lower in signal_lower or 
                any(part.lower() in sig_lower for part in signal_name.split('_') if len(part) >= 3)):
                similar_signals.append(sig)
        
        if similar_signals:
            print(f"Found {len(similar_signals)} similar signal(s):")
            for sig in similar_signals[:10]:  # Show first 10
                print(f"  {sig}")
        else:
            print("❌ No similar signals found")
        
        print()
        
        # 5. Test different variations of the signal name
        print("5. TESTING SIGNAL NAME VARIATIONS")
        print("-" * 40)
        
        variations = [
            signal_name,
            signal_name.upper(),
            signal_name.lower(),
            signal_name.replace('__', '_'),  # Single underscore
            signal_name.replace('_', ''),    # No underscores
        ]
        
        for variation in variations:
            found = False
            for _, sig in measurement_signals + characteristic_signals:
                if variation.lower() == sig.lower():
                    print(f"✅ Found exact match for variation: '{variation}' -> '{sig}'")
                    found = True
                    break
            if not found:
                print(f"❌ No match for variation: '{variation}'")
        
        print()
        
        # 6. Show context around potential matches
        print("6. SHOWING CONTEXT AROUND POTENTIAL MATCHES")
        print("-" * 50)
        
        # Look for lines that might contain the signal with different formatting
        potential_lines = []
        search_terms = ['LDP', 'TCO', 'IGA', 'ST']
        
        for i, line in enumerate(lines, 1):
            line_upper = line.upper()
            if any(term in line_upper for term in search_terms) and ('BEGIN' in line_upper):
                potential_lines.append((i, line.strip()))
        
        if potential_lines:
            print(f"Found {len(potential_lines)} potential line(s) with related terms:")
            for line_num, line_content in potential_lines[:10]:
                print(f"  Line {line_num}: {line_content}")
                
                # Show context (2 lines before and after)
                start_idx = max(0, line_num - 3)
                end_idx = min(len(lines), line_num + 2)
                print("    Context:")
                for j in range(start_idx, end_idx):
                    marker = "  >>> " if j == line_num - 1 else "      "
                    print(f"    {marker}Line {j+1}: {lines[j].strip()}")
                print()
        
        print("=" * 80)
        print("SUMMARY")
        print("=" * 80)
        print(f"Signal searched: {signal_name}")
        print(f"Total MEASUREMENT signals: {len(measurement_signals)}")
        print(f"Total CHARACTERISTIC signals: {len(characteristic_signals)}")
        print(f"Lines containing signal name: {len(exact_matches)}")
        print(f"Similar signals found: {len(similar_signals)}")
        
        if not exact_matches:
            print("\n🔍 RECOMMENDATIONS:")
            print("1. Check if the signal name is spelled correctly")
            print("2. Verify the signal exists in this A2L file")
            print("3. Try searching for parts of the signal name")
            print("4. Check if the signal might be in a different format")
            
    except Exception as e:
        print(f"❌ ERROR reading file: {e}")

def main():
    """Main function to run the debug script."""
    print("A2L Signal Search Debug Tool")
    print("=" * 40)
    
    # Default values
    default_signal = "LDP_TCO__IP_IGA_ST"
    default_file = "FS_RNCP3_3b_B40_4veh.a2l"
    
    # Get input from user
    signal_name = input(f"Enter signal name to debug (default: {default_signal}): ").strip()
    if not signal_name:
        signal_name = default_signal
    
    a2l_file = input(f"Enter A2L file path (default: {default_file}): ").strip()
    if not a2l_file:
        a2l_file = default_file
    
    print()
    debug_signal_search(signal_name, a2l_file)

if __name__ == "__main__":
    main()
