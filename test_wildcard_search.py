#!/usr/bin/env python3
"""
Test script for wildcard search functionality
"""

def test_wildcard_scoring():
    """Test the wildcard scoring algorithm."""
    print("TESTING WILDCARD SEARCH SCORING")
    print("=" * 50)
    
    # Sample signal names
    sample_signals = [
        "LDP_TCO__IP_IGA_ST",
        "LDP_CONTROL_STATUS", 
        "LDP_ACTIVE_FLAG",
        "LDP_WARNING_STATUS",
        "ENGINE_SPEED_KMH",
        "ENGINE_TEMP_CELSIUS",
        "ENGINE_LOAD_PERCENT",
        "TCO_IP_STATUS_FLAG",
        "IGA_STATUS_ACTIVE",
        "LDPM_TCO",
        "ABC_CONV_MON",
        "STATE_CLEAR_KWP",
        "SPEED_LIMIT_ACTIVE",
        "TEMP_ENGINE_COOLANT",
        "CONTROL_MODULE_STATUS"
    ]
    
    # Test wildcard search patterns
    test_patterns = [
        "*LDP*TCO*",           # Should find L<PERSON>_TCO__IP_IGA_ST, LDPM_TCO
        "*ENGINE*SPEED*",      # Should find ENGINE_SPEED_KMH
        "*STATUS*",            # Should find all signals with STATUS
        "*TEMP*ENGINE*",       # Should find TEMP_ENGINE_COOLANT
        "*CONTROL*",           # Should find LDP_CONTROL_STATUS, CONTROL_MODULE_STATUS
        "*TCO*IP*",            # Should find LDP_TCO__IP_IGA_ST, TCO_IP_STATUS_FLAG
        "*ACTIVE*",            # Should find LDP_ACTIVE_FLAG, SPEED_LIMIT_ACTIVE, IGA_STATUS_ACTIVE
        "*ABC*CONV*",          # Should find ABC_CONV_MON
    ]
    
    print("Sample signals:")
    for i, signal in enumerate(sample_signals, 1):
        print(f"  {i:2d}. {signal}")
    
    print(f"\nTesting wildcard patterns:")
    print("-" * 40)
    
    for pattern in test_patterns:
        print(f"\nPattern: '{pattern}'")
        
        # Simulate the wildcard search algorithm
        search_terms = [term.strip().lower() for term in pattern.replace('*', ' ').split() if term.strip()]
        print(f"  Search terms: {search_terms}")
        
        # Score each signal
        signal_scores = []
        for signal in sample_signals:
            score = calculate_wildcard_score(signal.lower(), search_terms)
            if score > 0:
                signal_scores.append((score, signal))
        
        # Sort by score and show results
        signal_scores.sort(key=lambda x: x[0], reverse=True)
        
        if signal_scores:
            print(f"  Matches (score: signal):")
            for score, signal in signal_scores[:5]:  # Show top 5
                print(f"    {score:3d}: {signal}")
        else:
            print(f"  No matches found")

def calculate_wildcard_score(signal_lower, search_terms):
    """Calculate how well a signal matches the search terms."""
    score = 0
    signal_parts = signal_lower.replace('_', ' ').split()
    
    # Check how many search terms are found in the signal
    terms_found = 0
    for term in search_terms:
        term_found = False
        
        # Check if term matches any part of the signal
        for part in signal_parts:
            if term in part:
                terms_found += 1
                term_found = True
                
                # Bonus points for exact part match
                if term == part:
                    score += 10
                # Bonus points for prefix match
                elif part.startswith(term):
                    score += 5
                # Regular points for contains match
                else:
                    score += 2
                break
        
        # Check if term is found anywhere in the signal (fallback)
        if not term_found and term in signal_lower:
            terms_found += 1
            score += 1
    
    # Bonus for finding all terms
    if terms_found == len(search_terms):
        score += 20
    
    # Penalty for missing terms
    missing_terms = len(search_terms) - terms_found
    score -= missing_terms * 5
    
    return max(0, score)  # Don't return negative scores

def demonstrate_wildcard_usage():
    """Demonstrate wildcard search usage examples."""
    print(f"\n" + "=" * 50)
    print("WILDCARD SEARCH USAGE EXAMPLES")
    print("=" * 50)
    
    examples = [
        {
            'pattern': '*LDP*TCO*',
            'description': 'Find signals containing both LDP and TCO',
            'matches': ['LDP_TCO__IP_IGA_ST', 'LDPM_TCO']
        },
        {
            'pattern': '*ENGINE*SPEED*',
            'description': 'Find engine speed related signals',
            'matches': ['ENGINE_SPEED_KMH', 'ENGINE_SPEED_RPM']
        },
        {
            'pattern': '*STATUS*ACTIVE*',
            'description': 'Find active status signals',
            'matches': ['STATUS_ACTIVE_FLAG', 'ACTIVE_STATUS_MONITOR']
        },
        {
            'pattern': '*TEMP*',
            'description': 'Find all temperature related signals',
            'matches': ['ENGINE_TEMP_CELSIUS', 'TEMP_ENGINE_COOLANT', 'AMBIENT_TEMP']
        },
        {
            'pattern': '*CONTROL*MODULE*',
            'description': 'Find control module signals',
            'matches': ['CONTROL_MODULE_STATUS', 'MODULE_CONTROL_FLAG']
        }
    ]
    
    print("Wildcard search examples:")
    print("-" * 30)
    
    for example in examples:
        print(f"\nPattern: {example['pattern']}")
        print(f"Purpose: {example['description']}")
        print(f"Example matches:")
        for match in example['matches']:
            print(f"  • {match}")

def show_search_features():
    """Show all search features available."""
    print(f"\n" + "=" * 50)
    print("COMPLETE SEARCH FEATURES")
    print("=" * 50)
    
    features = [
        "🔍 Real-time auto-suggestions (type 2+ characters)",
        "⭐ Wildcard search with * (e.g., '*LDP*TCO*')",
        "🎯 Flexible word order matching",
        "📊 Smart scoring algorithm",
        "🔤 Case-insensitive search",
        "⌨️ Keyboard navigation (Up/Down arrows)",
        "🖱️ Click or Enter to select",
        "🔧 Fuzzy matching for typos",
        "📈 Similarity percentage display",
        "🏷️ Object type display (MEASUREMENT, AXIS_PTS, etc.)",
        "🎨 Professional dropdown interface",
        "⚡ Fast background indexing",
        "🔄 Auto-hide suggestions",
        "💡 Built-in help tips"
    ]
    
    print("Available search features:")
    for feature in features:
        print(f"  {feature}")
    
    print(f"\nSearch patterns supported:")
    patterns = [
        "Normal: 'LDP_TCO' → finds signals starting with LDP_TCO",
        "Partial: 'TCO' → finds signals containing TCO",
        "Wildcard: '*LDP*TCO*' → finds signals with both LDP and TCO",
        "Flexible: '*ENGINE*SPEED*' → finds ENGINE_SPEED_KMH, SPEED_ENGINE_RPM",
        "Multiple: '*STATUS*ACTIVE*FLAG*' → finds complex patterns"
    ]
    
    for pattern in patterns:
        print(f"  • {pattern}")

def test_gui_integration():
    """Test GUI integration instructions."""
    print(f"\n" + "=" * 50)
    print("GUI INTEGRATION TEST")
    print("=" * 50)
    
    print("To test the wildcard search in GUI:")
    print("-" * 35)
    
    steps = [
        "1. Run: python a2l_gui.py",
        "2. Load your A2L file (wait for indexing)",
        "3. Try these wildcard searches:",
        "   • Type: *LDP*TCO*",
        "   • Type: *ENGINE*SPEED*", 
        "   • Type: *STATUS*",
        "   • Type: *AXIS*PTS*",
        "4. Watch suggestions appear with scores",
        "5. Use arrow keys to navigate",
        "6. Press Enter or click to select",
        "7. See the signal details displayed"
    ]
    
    for step in steps:
        print(f"  {step}")
    
    print(f"\nExpected behavior:")
    behaviors = [
        "✅ Suggestions appear as you type wildcard patterns",
        "✅ Results ranked by relevance (best matches first)",
        "✅ Object types shown (MEASUREMENT, AXIS_PTS, etc.)",
        "✅ Up to 15 suggestions displayed",
        "✅ Flexible matching works in any word order",
        "✅ Help tip shows wildcard usage example"
    ]
    
    for behavior in behaviors:
        print(f"  {behavior}")

if __name__ == "__main__":
    test_wildcard_scoring()
    demonstrate_wildcard_usage()
    show_search_features()
    test_gui_integration()
