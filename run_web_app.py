#!/usr/bin/env python3
"""
Startup script for A2L Signal Search Web Application

This script starts the Flask web application for A2L signal searching.
"""

import os
import sys
import subprocess

def check_requirements():
    """Check if required packages are installed."""
    try:
        import flask
        print("✓ Flask is installed")
        return True
    except ImportError:
        print("✗ Flask is not installed")
        return False

def install_requirements():
    """Install required packages."""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("✗ Failed to install requirements")
        return False

def create_directories():
    """Create necessary directories."""
    directories = ['uploads', 'templates']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✓ Created directory: {directory}")

def main():
    """Main function to start the web application."""
    print("A2L Signal Search Web Application Startup")
    print("=" * 50)
    
    # Create necessary directories
    create_directories()
    
    # Check and install requirements
    if not check_requirements():
        print("\nInstalling requirements...")
        if not install_requirements():
            print("Failed to install requirements. Please install Flask manually:")
            print("pip install Flask==2.3.3 Werkzeug==2.3.7")
            return
    
    # Check if app.py exists
    if not os.path.exists('app.py'):
        print("✗ app.py not found. Please ensure all files are in the correct location.")
        return
    
    # Check if template exists
    if not os.path.exists('templates/index.html'):
        print("✗ templates/index.html not found. Please ensure all files are in the correct location.")
        return
    
    print("\n" + "=" * 50)
    print("Starting A2L Signal Search Web Application...")
    print("=" * 50)
    print("🌐 Web interface will be available at: http://localhost:5000")
    print("📁 Upload your A2L file and search for signals")
    print("🔍 Case-insensitive signal search with detailed information")
    print("\nPress Ctrl+C to stop the server")
    print("=" * 50)
    
    # Start the Flask application
    try:
        from app import app
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n\nServer stopped by user")
    except Exception as e:
        print(f"\nError starting server: {e}")

if __name__ == "__main__":
    main()
