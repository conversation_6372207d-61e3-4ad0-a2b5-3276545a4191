# 🎯 Enhanced Filtering System Guide

## ⭐ **New Features: CHARACTERISTIC Sub-Types + Select All**

Your A2L Signal Search application now has advanced filtering with CHARACTERISTIC sub-type detection and convenient Select All functionality!

## 🔍 **CHARACTERISTIC Sub-Types**

### **Automatic Detection**
The application now automatically detects and categorizes CHARACTERISTIC objects into three sub-types:

| Sub-Type | A2L Keyword | Description | Example Use Cases |
|----------|-------------|-------------|-------------------|
| **MAP** | `MAP` | Multi-dimensional calibration maps | Engine torque maps, fuel injection maps |
| **VAL** | `VALUE` | Scalar parameters (single values) | Maximum speed limits, threshold values |
| **CUR** | `CURVE` | 1D curves/tables | Fuel injection curves, temperature curves |

### **Enhanced Display**
Suggestions now show detailed type information:
- `engine_torque_map (CHARACTERISTIC-MAP)`
- `max_engine_speed (CHARACTERISTIC-VAL)`
- `fuel_injection_curve (CHARACTERISTIC-CUR)`

## 🎛️ **New Filter Interface**

### **Filter Categories**
```
┌─ Filter by Object Type ─────────────────────────────────┐
│ ☑ SELECT ALL                                           │
│                                                         │
│ ☑ MEASUREMENT    ☑ AXIS_PTS    ☑ OTHER                │
│                                                         │
│ CHARACTERISTIC Types:                                   │
│ ☑ MAP (Maps)    ☑ VAL (Scalars)    ☑ CUR (Curves)    │
└─────────────────────────────────────────────────────────┘
```

### **Filter Options**
1. **SELECT ALL** - Master toggle for all filters
2. **MEASUREMENT** - All measurement signals
3. **AXIS_PTS** - Axis point definitions (like LDPM_TCO)
4. **OTHER** - COMPU_METHOD, COMPU_TAB, GROUP, etc.
5. **MAP (Maps)** - Multi-dimensional characteristic maps
6. **VAL (Scalars)** - Single-value characteristic parameters
7. **CUR (Curves)** - 1D characteristic curves

## 🎯 **Select All Functionality**

### **Smart Behavior**
- **Check "SELECT ALL"** → Enables all individual filters
- **Uncheck "SELECT ALL"** → Disables all individual filters
- **Auto-updates** → Reflects state of individual filters

### **State Management**
- ✅ **All checked** → SELECT ALL shows checked
- ❌ **None checked** → SELECT ALL shows unchecked
- ⚪ **Some checked** → SELECT ALL state preserved

## 🚀 **Usage Scenarios**

### **Scenario 1: Find Only Calibration Maps**
```
Goal: Find multi-dimensional calibration maps
Steps:
1. Uncheck "SELECT ALL"
2. Check only "MAP (Maps)"
3. Type "*ENGINE*" or "*TORQUE*"
4. See only MAP-type characteristics

Results: engine_torque_map (CHARACTERISTIC-MAP)
         fuel_injection_map (CHARACTERISTIC-MAP)
```

### **Scenario 2: Find Scalar Parameters**
```
Goal: Find single-value parameters
Steps:
1. Uncheck all except "VAL (Scalars)"
2. Type "*LIMIT*" or "*MAX*"
3. See only scalar characteristics

Results: max_engine_speed (CHARACTERISTIC-VAL)
         speed_limit_active (CHARACTERISTIC-VAL)
```

### **Scenario 3: Find All Engine-Related Objects**
```
Goal: Find everything related to engine
Steps:
1. Keep "SELECT ALL" checked (all filters active)
2. Type "*ENGINE*"
3. See all object types with "ENGINE"

Results: engine_speed (MEASUREMENT)
         engine_torque_map (CHARACTERISTIC-MAP)
         max_engine_speed (CHARACTERISTIC-VAL)
         engine_speed_axis (AXIS_PTS)
```

### **Scenario 4: Find Only AXIS_PTS Objects**
```
Goal: Find axis point definitions like LDPM_TCO
Steps:
1. Uncheck "SELECT ALL"
2. Check only "AXIS_PTS"
3. Type "*TCO*" or "*SPEED*"

Results: LDPM_TCO (AXIS_PTS)
         engine_speed_axis (AXIS_PTS)
```

## 🎨 **Enhanced User Experience**

### **Visual Improvements**
- 🏷️ **Detailed Type Labels** - Shows exact sub-type information
- 📊 **Organized Layout** - Logical grouping of filter options
- ⭐ **Master Control** - SELECT ALL for quick toggling
- 💡 **Clear Categories** - Separate sections for different object types

### **Workflow Benefits**
- 🎯 **Targeted Search** - Find specific object types quickly
- 🔍 **Reduced Noise** - Hide irrelevant results
- ⚡ **Faster Discovery** - Less scrolling through suggestions
- 🎛️ **Flexible Control** - Fine-tune search scope

## 🔧 **Technical Implementation**

### **Sub-Type Detection Algorithm**
```python
# Scan CHARACTERISTIC blocks for type keywords
if re.search(r'\bMAP\b', content, re.IGNORECASE):
    subtype = 'MAP'
elif re.search(r'\bVALUE\b', content, re.IGNORECASE):
    subtype = 'VAL'
elif re.search(r'\bCURVE\b', content, re.IGNORECASE):
    subtype = 'CUR'
```

### **Filter Logic**
```python
def _is_signal_type_allowed(self, signal_type):
    if signal_type == 'CHAR_MAP':
        return self.filter_char_map.get()
    elif signal_type == 'CHAR_VAL':
        return self.filter_char_val.get()
    elif signal_type == 'CHAR_CUR':
        return self.filter_char_cur.get()
    # ... other types
```

## 📊 **Filter Combinations**

### **Common Filter Combinations**
| Use Case | Enabled Filters | Purpose |
|----------|----------------|---------|
| **Calibration Work** | MAP, VAL, CUR | All characteristic types |
| **Data Acquisition** | MEASUREMENT | Only measurement signals |
| **Axis Definition** | AXIS_PTS | Only axis point objects |
| **Maps Only** | MAP | Multi-dimensional calibrations |
| **Parameters Only** | VAL | Scalar parameters |
| **Everything** | SELECT ALL | Complete search |

### **Advanced Combinations**
- **Calibration + Measurements**: MAP, VAL, CUR, MEASUREMENT
- **Structure Definition**: AXIS_PTS, OTHER
- **Data Objects Only**: MEASUREMENT, AXIS_PTS
- **Calibration Maps**: MAP, CUR (exclude scalars)

## 🎯 **Benefits Summary**

### **For Calibration Engineers**
- 🎯 **Find maps quickly** - Filter to MAP type only
- 📊 **Separate scalars** - VAL filter for parameters
- 🔄 **Curve analysis** - CUR filter for 1D tables

### **For Data Acquisition**
- 📈 **Measurement focus** - MEASUREMENT filter only
- 🎛️ **Axis points** - AXIS_PTS for signal structure
- 🔍 **Clean results** - No calibration noise

### **For System Integration**
- 🌐 **Complete view** - SELECT ALL for overview
- 🎯 **Targeted search** - Specific object types
- ⚡ **Fast switching** - Quick filter changes

## 🚀 **Getting Started**

### **Test the New Features**
1. **Run the GUI**: `python a2l_gui.py`
2. **Load A2L file** and wait for indexing
3. **Try SELECT ALL** - Check/uncheck to see effect
4. **Test sub-types** - Enable only MAP, search "*ENGINE*"
5. **Use keyboard navigation** - Arrow keys work with filters
6. **Combine with wildcards** - `*TORQUE*` with MAP filter

### **Best Practices**
- 🎯 **Start broad** - Use SELECT ALL, then narrow down
- 🔍 **Use sub-types** - Leverage MAP/VAL/CUR for precision
- ⚡ **Quick toggle** - SELECT ALL for fast enable/disable
- 🎛️ **Save time** - Set filters once, search multiple terms

Your A2L Signal Search now has **professional-grade filtering** with intelligent CHARACTERISTIC sub-type detection and convenient Select All functionality! 🎉
