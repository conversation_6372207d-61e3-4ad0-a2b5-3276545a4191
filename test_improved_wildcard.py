#!/usr/bin/env python3
"""
Test script for the improved wildcard search algorithm
"""

def test_wildcard_scoring():
    """Test the improved wildcard scoring algorithm."""
    print("TESTING IMPROVED WILDCARD SEARCH ALGORITHM")
    print("=" * 55)
    
    # Sample signals that might be in an A2L file
    test_signals = [
        "FAC_AFU_RATIO_ISA",      # Should score highest for "fac*afu"
        "CPPWM_FAC_DEC",          # Should score lower (has fac but no afu)
        "AFU_FAC_CONTROL",        # Should score medium (has both but wrong order)
        "FAC_CONTROL_AFU_RATIO",  # Should score high (correct order, close proximity)
        "ENGINE_FAC_AFU_MAP",     # Should score high (correct order)
        "FAC_MULTIPLIER",         # Should score low (only has fac)
        "AFU_SENSOR_VALUE",       # Should score low (only has afu)
        "FUEL_FAC_AFU_CALC",      # Should score high (correct order)
        "FAC_AFU_DIAGNOSTIC",     # Should score very high (starts with fac, has afu)
        "LAMBDA_FAC_AFU_CORR",    # Should score high (correct order)
    ]
    
    # Test the scoring algorithm
    search_pattern = "fac*afu"
    search_terms = [term.strip().lower() for term in search_pattern.replace('*', ' ').split() if term.strip()]
    
    print(f"Search pattern: '{search_pattern}'")
    print(f"Search terms: {search_terms}")
    print()
    
    # Calculate scores for each signal
    signal_scores = []
    for signal in test_signals:
        score = calculate_improved_wildcard_score(signal.lower(), search_terms)
        signal_scores.append((score, signal))
    
    # Sort by score (highest first)
    signal_scores.sort(key=lambda x: x[0], reverse=True)
    
    print("Ranking Results (Score: Signal):")
    print("-" * 35)
    for i, (score, signal) in enumerate(signal_scores, 1):
        print(f"{i:2d}. {score:3d}: {signal}")
    
    print()
    print("Expected top results for 'fac*afu':")
    print("1. FAC_AFU_RATIO_ISA (starts with fac, has afu)")
    print("2. FAC_AFU_DIAGNOSTIC (starts with fac, has afu)")
    print("3. FAC_CONTROL_AFU_RATIO (correct order, close)")
    print("4. ENGINE_FAC_AFU_MAP (correct order)")

def calculate_improved_wildcard_score(signal_lower, search_terms):
    """Calculate wildcard score using the improved algorithm."""
    if not search_terms:
        return 0
    
    score = 0
    signal_parts = signal_lower.replace('_', ' ').split()
    
    # Find positions of each search term in the signal
    term_positions = []
    terms_found = 0
    
    for i, term in enumerate(search_terms):
        best_position = -1
        best_score = 0
        term_found = False
        
        # Check each part of the signal
        for part_idx, part in enumerate(signal_parts):
            if term in part:
                term_found = True
                part_score = 0
                
                # Score based on match quality
                if term == part:
                    part_score = 20  # Exact match
                elif part.startswith(term):
                    part_score = 15  # Prefix match
                elif part.endswith(term):
                    part_score = 12  # Suffix match
                else:
                    part_score = 8   # Contains match
                
                # Bonus for early position in signal
                position_bonus = max(0, 10 - part_idx * 2)
                part_score += position_bonus
                
                # Keep track of best match for this term
                if part_score > best_score:
                    best_score = part_score
                    best_position = part_idx
        
        # Also check if term appears anywhere in the full signal (fallback)
        if not term_found and term in signal_lower:
            term_found = True
            best_score = 3  # Lower score for non-word-boundary matches
            best_position = signal_lower.find(term)
        
        if term_found:
            terms_found += 1
            score += best_score
            term_positions.append((i, best_position, best_score))
    
    # Bonus for finding all terms
    if terms_found == len(search_terms):
        score += 30
    
    # Check term order bonus (terms should appear in search order)
    if len(term_positions) >= 2:
        order_bonus = 0
        for i in range(len(term_positions) - 1):
            current_pos = term_positions[i][1]
            next_pos = term_positions[i + 1][1]
            
            if current_pos < next_pos:  # Correct order
                order_bonus += 10
            elif current_pos > next_pos:  # Wrong order
                order_bonus -= 5
        
        score += order_bonus
    
    # Proximity bonus (terms close together score higher)
    if len(term_positions) >= 2:
        positions = [pos[1] for pos in term_positions if pos[1] >= 0]
        if len(positions) >= 2:
            max_distance = max(positions) - min(positions)
            if max_distance <= 3:  # Terms within 3 words of each other
                score += 15
            elif max_distance <= 5:  # Terms within 5 words
                score += 8
    
    # Penalty for missing terms
    missing_terms = len(search_terms) - terms_found
    score -= missing_terms * 15
    
    # Bonus for signal starting with first search term
    if search_terms and signal_lower.startswith(search_terms[0]):
        score += 25
    
    return max(0, score)  # Don't return negative scores

def test_specific_cases():
    """Test specific problematic cases."""
    print(f"\n" + "=" * 55)
    print("TESTING SPECIFIC CASES")
    print("=" * 55)
    
    test_cases = [
        {
            'pattern': 'fac*afu',
            'signals': [
                'FAC_AFU_RATIO_ISA',
                'CPPWM_FAC_DEC',
                'AFU_FAC_CONTROL'
            ],
            'expected_winner': 'FAC_AFU_RATIO_ISA'
        },
        {
            'pattern': 'engine*speed',
            'signals': [
                'ENGINE_SPEED_KMH',
                'SPEED_ENGINE_RPM',
                'ENGINE_CONTROL_SPEED'
            ],
            'expected_winner': 'ENGINE_SPEED_KMH'
        },
        {
            'pattern': 'ldp*tco',
            'signals': [
                'LDP_TCO__IP_IGA_ST',
                'TCO_LDP_STATUS',
                'LDP_CONTROL_TCO_FLAG'
            ],
            'expected_winner': 'LDP_TCO__IP_IGA_ST'
        }
    ]
    
    for case in test_cases:
        print(f"\nPattern: '{case['pattern']}'")
        search_terms = [term.strip().lower() for term in case['pattern'].replace('*', ' ').split() if term.strip()]
        
        scores = []
        for signal in case['signals']:
            score = calculate_improved_wildcard_score(signal.lower(), search_terms)
            scores.append((score, signal))
        
        scores.sort(key=lambda x: x[0], reverse=True)
        
        print("Results:")
        for i, (score, signal) in enumerate(scores, 1):
            marker = "🏆" if signal == case['expected_winner'] else "  "
            print(f"  {marker} {i}. {score:3d}: {signal}")
        
        winner = scores[0][1]
        if winner == case['expected_winner']:
            print(f"  ✅ Correct winner: {winner}")
        else:
            print(f"  ❌ Expected: {case['expected_winner']}, Got: {winner}")

def show_algorithm_improvements():
    """Show the improvements made to the algorithm."""
    print(f"\n" + "=" * 55)
    print("ALGORITHM IMPROVEMENTS")
    print("=" * 55)
    
    improvements = [
        {
            'aspect': 'Word Order Consideration',
            'old': 'No consideration of term order',
            'new': '+10 bonus for correct order, -5 penalty for wrong order'
        },
        {
            'aspect': 'Proximity Scoring',
            'old': 'No proximity consideration',
            'new': '+15 bonus if terms within 3 words, +8 if within 5 words'
        },
        {
            'aspect': 'Position Preference',
            'old': 'No position preference',
            'new': '+25 bonus for starting with first term, position bonus for early matches'
        },
        {
            'aspect': 'Match Quality',
            'old': 'Simple contains matching',
            'new': 'Exact (20), Prefix (15), Suffix (12), Contains (8) scoring'
        },
        {
            'aspect': 'Missing Term Penalty',
            'old': '-5 per missing term',
            'new': '-15 per missing term (stronger penalty)'
        },
        {
            'aspect': 'Complete Match Bonus',
            'old': '+20 for finding all terms',
            'new': '+30 for finding all terms (higher reward)'
        }
    ]
    
    for improvement in improvements:
        print(f"\n🔧 {improvement['aspect']}:")
        print(f"   Old: {improvement['old']}")
        print(f"   New: {improvement['new']}")

if __name__ == "__main__":
    test_wildcard_scoring()
    test_specific_cases()
    show_algorithm_improvements()
