# A2L Signal Search - Quick Start Guide

## 🚀 What's New

✅ **Commented out fields**: Resolution, accuracy, and format are now commented out in main.py (can be enabled later)

✅ **New Web Interface**: Modern, responsive web UI with file upload and search functionality

✅ **Description-first display**: Shows signal description immediately, with "More Info" button for full details

## 🌐 Web Interface (Recommended)

### Quick Start
1. **Double-click** `start_web_app.bat` (Windows) or run:
   ```bash
   python run_web_app.py
   ```

2. **Open browser** and go to: `http://localhost:5000`

3. **Upload** your A2L file using the upload button

4. **Search** for any signal name (case insensitive)

5. **View** description immediately, click "More Info" for full details

### Features
- 📁 **File Upload**: Drag & drop or click to upload A2L files
- 🔍 **Smart Search**: Case-insensitive signal search
- 📋 **Description First**: See signal description immediately
- ℹ️ **Expandable Details**: Click "More Info" for comprehensive signal information
- 📱 **Responsive**: Works on desktop, tablet, and mobile
- 🔒 **Secure**: File validation and session management

## 💻 Command Line Interface

### Basic Usage
```bash
# Search for a signal
python main.py signal_name

# Search in specific file
python main.py signal_name your_file.a2l

# Interactive mode
python main.py
```

## 📁 File Structure

```
Label_search_app/
├── 🌐 Web Application
│   ├── app.py                 # Flask web server
│   ├── templates/
│   │   └── index.html         # Web interface
│   ├── run_web_app.py         # Startup script
│   ├── start_web_app.bat      # Windows batch file
│   └── requirements.txt       # Dependencies
│
├── 💻 Command Line
│   ├── main.py                # Main CLI application
│   ├── a2l_stream_search.py   # Alternative implementation
│   └── a2l_search.py          # Another alternative
│
├── 🧪 Testing
│   ├── test_web_app.py        # Web app tests
│   ├── demo_and_test.py       # Comprehensive tests
│   └── validate_code.py       # Code validation
│
└── 📚 Documentation
    ├── README.md              # Full documentation
    └── QUICK_START.md         # This file
```

## 🎯 Signal Information Displayed

### Description View (Default)
- Signal name
- Signal description
- Signal type (MEASUREMENT/CHARACTERISTIC)

### More Info View (Click "More Info")
- All above plus:
- Data type (for MEASUREMENT)
- ECU Address (for MEASUREMENT)
- Characteristic type (for CHARACTERISTIC)
- Address (for CHARACTERISTIC)
- Display identifier
- Conversion method
- Value ranges
- Bit mask (if applicable)

### Commented Out (For Future Use)
- Resolution
- Accuracy
- Format

## 🔧 Requirements

### Web Application
- Python 3.6+
- Flask 2.3.3
- Werkzeug 2.3.7

### Command Line
- Python 3.6+ only (no external dependencies)

## 🚀 Getting Started

### Option 1: Web Interface (Easiest)
1. Double-click `start_web_app.bat`
2. Wait for "Running on http://127.0.0.1:5000"
3. Open browser to `http://localhost:5000`
4. Upload your A2L file
5. Search for signals!

### Option 2: Command Line
1. Open terminal/command prompt
2. Navigate to the folder
3. Run: `python main.py your_signal_name`

## 🎨 Web Interface Preview

```
┌─────────────────────────────────────────┐
│           A2L Signal Search             │
│     Upload your A2L file and search     │
├─────────────────────────────────────────┤
│ 📁 Upload A2L File                      │
│ [Choose A2L File]                       │
├─────────────────────────────────────────┤
│ 🔍 Search Signal                        │
│ [Enter signal name...]                  │
│ [Search Signal]                         │
├─────────────────────────────────────────┤
│ 📋 Results                              │
│ Signal: eng_hour_ofs                    │
│ Description: LIF NVRAM, ECU replacement │
│ offset for ENG HOUR                     │
│ [More Info]                             │
└─────────────────────────────────────────┘
```

## 🆘 Troubleshooting

### Web App Won't Start
- Install Flask: `pip install Flask==2.3.3 Werkzeug==2.3.7`
- Check Python version: `python --version` (need 3.6+)

### Signal Not Found
- Check signal name spelling
- Try different case (uppercase/lowercase)
- Verify A2L file is uploaded correctly

### Large File Processing
- Web interface handles large files efficiently
- Progress indicators show processing status
- Use streaming search for memory efficiency

## 📞 Support

If you encounter issues:
1. Check the console/terminal for error messages
2. Verify all files are present
3. Ensure Python and dependencies are installed
4. Try the command line version as fallback

## 🎉 Success!

You now have a complete A2L signal search solution with both web and command line interfaces!

**Web Interface**: Modern, user-friendly, perfect for interactive use
**Command Line**: Fast, scriptable, perfect for automation

Choose the interface that best fits your workflow! 🚀
