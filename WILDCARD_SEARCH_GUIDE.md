# 🔍 Wildcard Search Guide

## ⭐ **New Feature: Advanced Wildcard Search**

Your A2L Signal Search application now supports powerful wildcard search with flexible word order matching!

## 🎯 **How to Use Wildcard Search**

### **Basic Syntax**
- Use `*` as a wildcard character
- Type signal parts in any order
- Separate terms with `*`

### **Examples**

| Search Pattern | What It Finds | Example Results |
|----------------|---------------|-----------------|
| `*LDP*TCO*` | Signals with both LDP and TCO | `LDP_TCO__IP_IGA_ST`, `LDPM_TCO` |
| `*ENGINE*SPEED*` | Engine speed signals | `ENGINE_SPEED_KMH`, `SPEED_ENGINE_RPM` |
| `*STATUS*ACTIVE*` | Active status signals | `STATUS_ACTIVE_FLAG`, `ACTIVE_STATUS_MONITOR` |
| `*TEMP*ENGINE*` | Engine temperature signals | `TEMP_ENGINE_COOLANT`, `ENGINE_TEMP_CELSIUS` |
| `*CONTROL*MODULE*` | Control module signals | `CONTROL_MODULE_STATUS`, `MODULE_CONTROL_FLAG` |

## 🔧 **Search Modes**

### **1. Standard Search (No Wildcards)**
```
Input: "LDP_TCO"
Results: Signals starting with "LDP_TCO"
```

### **2. Wildcard Search (With *)**
```
Input: "*LDP*TCO*"
Results: Signals containing both "LDP" and "TCO" anywhere
```

### **3. Flexible Order Matching**
```
Input: "*TCO*LDP*"
Results: Same as "*LDP*TCO*" - order doesn't matter!
```

## 📊 **Smart Scoring Algorithm**

The wildcard search uses intelligent scoring to rank results:

### **Scoring Rules**
- **+20 points**: All search terms found
- **+10 points**: Exact word match (e.g., "LDP" matches "LDP" exactly)
- **+5 points**: Prefix match (e.g., "ENG" matches "ENGINE")
- **+2 points**: Contains match (e.g., "GIN" matches "ENGINE")
- **+1 point**: Found anywhere in signal name
- **-5 points**: Each missing search term

### **Example Scoring**
For search `*LDP*TCO*`:
```
LDP_TCO__IP_IGA_ST → Score: 42
  +20 (all terms found)
  +10 (exact "LDP" match)
  +10 (exact "TCO" match)
  +2  (bonus points)

LDPM_TCO → Score: 27
  +20 (all terms found)
  +2  ("LDP" in "LDPM")
  +10 (exact "TCO" match)
  -5  (penalty adjustments)
```

## 🎨 **User Interface Features**

### **Visual Indicators**
- 💡 **Help Tip**: Shows wildcard usage example
- 📋 **Dropdown**: Up to 15 ranked suggestions
- 🏷️ **Type Labels**: Shows object type (MEASUREMENT, AXIS_PTS, etc.)
- ⭐ **Smart Ranking**: Best matches appear first

### **Keyboard Navigation**
- **Type**: Start typing with `*` for wildcard mode
- **Navigate**: Use ↑↓ arrow keys
- **Select**: Press Enter or double-click
- **Hide**: Click elsewhere or press Escape

## 🚀 **Usage Scenarios**

### **Scenario 1: Finding Related Signals**
```
Problem: Need all LDP (Lane Departure Prevention) signals
Solution: Type "*LDP*"
Results: LDP_TCO__IP_IGA_ST, LDP_CONTROL_STATUS, LDP_ACTIVE_FLAG, etc.
```

### **Scenario 2: Cross-Reference Search**
```
Problem: Find signals related to both "ENGINE" and "SPEED"
Solution: Type "*ENGINE*SPEED*"
Results: ENGINE_SPEED_KMH, SPEED_ENGINE_RPM, ENGINE_SPEED_SENSOR, etc.
```

### **Scenario 3: Partial Memory Search**
```
Problem: Remember signal has "TCO" and "STATUS" but not exact name
Solution: Type "*TCO*STATUS*"
Results: TCO_STATUS_FLAG, STATUS_TCO_ACTIVE, etc.
```

### **Scenario 4: Object Type Search**
```
Problem: Find all AXIS_PTS objects with "SPEED"
Solution: Type "*SPEED*" → See (AXIS_PTS) labels in results
Results: SPEED_AXIS_PTS (AXIS_PTS), ENGINE_SPEED_AXIS (AXIS_PTS), etc.
```

## 🔍 **Advanced Search Patterns**

### **Multiple Wildcards**
```
*ENGINE*TEMP*SENSOR* → ENGINE_TEMP_SENSOR_1, TEMP_ENGINE_SENSOR_MAIN
*CONTROL*MODULE*STATUS* → CONTROL_MODULE_STATUS_FLAG, MODULE_CONTROL_STATUS
```

### **Single Character Patterns**
```
*A* → All signals containing "A"
*ST* → All signals containing "ST"
```

### **Complex Patterns**
```
*LDP*TCO*IP*IGA* → Very specific multi-term search
*ENGINE*SPEED*KMH* → Engine speed in km/h units
```

## ⚡ **Performance Features**

### **Fast Search**
- **Pre-indexed**: All signals indexed when A2L file loads
- **Real-time**: Suggestions appear as you type
- **Efficient**: Smart algorithms for quick results

### **Memory Optimized**
- **Lightweight**: Only signal names and types stored
- **Responsive**: No lag during search
- **Scalable**: Works with large A2L files

## 🎯 **Best Practices**

### **Effective Search Tips**
1. **Start Broad**: Use `*ENGINE*` to see all engine-related signals
2. **Narrow Down**: Add more terms like `*ENGINE*SPEED*`
3. **Use Key Terms**: Focus on important words in signal names
4. **Try Variations**: If no results, try different word combinations

### **Common Patterns**
- **By Function**: `*CONTROL*`, `*STATUS*`, `*FLAG*`, `*SENSOR*`
- **By System**: `*ENGINE*`, `*LDP*`, `*TCO*`, `*IGA*`
- **By Type**: Look for (MEASUREMENT), (AXIS_PTS), (CHARACTERISTIC) labels
- **By Units**: `*KMH*`, `*RPM*`, `*CELSIUS*`, `*PERCENT*`

## 🔧 **Technical Implementation**

### **Algorithm Overview**
1. **Parse Input**: Extract search terms from wildcard pattern
2. **Score Signals**: Calculate relevance score for each signal
3. **Rank Results**: Sort by score (highest first)
4. **Display**: Show top 15 results with type labels

### **Scoring Factors**
- **Term Coverage**: How many search terms are found
- **Match Quality**: Exact vs partial vs contains matches
- **Position Bonus**: Earlier matches get higher scores
- **Completeness**: Bonus for finding all terms

## 🎉 **Benefits**

### **For Users**
✅ **Flexible Search**: Find signals without knowing exact names
✅ **Smart Suggestions**: Best matches appear first
✅ **Fast Discovery**: Quickly find related signals
✅ **Error Tolerant**: Works even with partial memory

### **For Teams**
✅ **Consistent Results**: Same search gives same ranked results
✅ **Easy Learning**: Intuitive wildcard syntax
✅ **Comprehensive**: Covers all A2L object types
✅ **Professional**: Modern search engine experience

## 🚀 **Getting Started**

1. **Load A2L File**: Wait for signal indexing to complete
2. **Try Basic Wildcard**: Type `*LDP*` and see suggestions
3. **Experiment**: Try `*ENGINE*SPEED*`, `*STATUS*`, etc.
4. **Use Navigation**: Arrow keys and Enter for selection
5. **Explore Results**: Click "More Info" for full signal details

Your A2L Signal Search now has Google-like search capabilities with intelligent wildcard matching! 🎯
