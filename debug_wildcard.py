#!/usr/bin/env python3
"""
Debug script to test wildcard search for ip_teg_tia_add with *ip*add
"""

def debug_wildcard_search():
    """Debug the wildcard search algorithm."""
    print("DEBUGGING WILDCARD SEARCH: *ip*add")
    print("=" * 50)
    
    # Test signal
    test_signal = "ip_teg_tia_add"
    search_pattern = "*ip*add"
    
    print(f"Signal: {test_signal}")
    print(f"Search: {search_pattern}")
    print()
    
    # Extract search terms like the app does
    search_terms = [term.strip().lower() for term in search_pattern.replace('*', ' ').split() if term.strip()]
    print(f"Search terms: {search_terms}")
    print()
    
    # Test the scoring
    score = calculate_wildcard_score_debug(test_signal.lower(), search_terms)
    print(f"Final score: {score}")
    
    # Test other similar signals
    test_signals = [
        "ip_teg_tia_add",
        "ip_control_add",
        "add_ip_value",
        "ip_sensor_data",
        "add_control_ip",
        "ip_add_function",
        "other_signal_name"
    ]
    
    print(f"\n" + "=" * 50)
    print("TESTING MULTIPLE SIGNALS")
    print("=" * 50)
    
    for signal in test_signals:
        score = calculate_wildcard_score_debug(signal.lower(), search_terms)
        print(f"{signal:<20} → Score: {score}")

def calculate_wildcard_score_debug(signal_lower, search_terms):
    """Debug version of the wildcard scoring algorithm."""
    if not search_terms:
        return 0
    
    print(f"\nScoring signal: {signal_lower}")
    print(f"Search terms: {search_terms}")
    
    score = 0
    signal_parts = signal_lower.replace('_', ' ').split()
    print(f"Signal parts: {signal_parts}")
    
    # Find positions of each search term in the signal
    term_positions = []
    terms_found = 0
    
    for i, term in enumerate(search_terms):
        print(f"\nLooking for term '{term}':")
        best_position = -1
        best_score = 0
        term_found = False
        
        # Check each part of the signal
        for part_idx, part in enumerate(signal_parts):
            if term in part:
                term_found = True
                part_score = 0
                
                # Score based on match quality
                if term == part:
                    part_score = 20  # Exact match
                    print(f"  Found exact match in part '{part}' at position {part_idx}: +20")
                elif part.startswith(term):
                    part_score = 15  # Prefix match
                    print(f"  Found prefix match in part '{part}' at position {part_idx}: +15")
                elif part.endswith(term):
                    part_score = 12  # Suffix match
                    print(f"  Found suffix match in part '{part}' at position {part_idx}: +12")
                else:
                    part_score = 8   # Contains match
                    print(f"  Found contains match in part '{part}' at position {part_idx}: +8")
                
                # Bonus for early position in signal
                position_bonus = max(0, 10 - part_idx * 2)
                part_score += position_bonus
                print(f"  Position bonus: +{position_bonus}")
                
                # Keep track of best match for this term
                if part_score > best_score:
                    best_score = part_score
                    best_position = part_idx
        
        # Also check if term appears anywhere in the full signal (fallback)
        if not term_found and term in signal_lower:
            term_found = True
            best_score = 3  # Lower score for non-word-boundary matches
            best_position = signal_lower.find(term)
            print(f"  Found fallback match in full signal: +3")
        
        if term_found:
            terms_found += 1
            score += best_score
            term_positions.append((i, best_position, best_score))
            print(f"  Term '{term}' found! Best score: {best_score}")
        else:
            print(f"  Term '{term}' NOT found!")
    
    print(f"\nTerms found: {terms_found}/{len(search_terms)}")
    print(f"Current score: {score}")
    
    # Bonus for finding all terms
    if terms_found == len(search_terms):
        score += 30
        print(f"All terms found bonus: +30")
    
    # Check term order bonus (terms should appear in search order)
    if len(term_positions) >= 2:
        order_bonus = 0
        print(f"Checking term order...")
        for i in range(len(term_positions) - 1):
            current_pos = term_positions[i][1]
            next_pos = term_positions[i + 1][1]
            
            if current_pos < next_pos:  # Correct order
                order_bonus += 10
                print(f"  Correct order: +10")
            elif current_pos > next_pos:  # Wrong order
                order_bonus -= 5
                print(f"  Wrong order: -5")
        
        score += order_bonus
        print(f"Order bonus total: {order_bonus}")
    
    # Proximity bonus (terms close together score higher)
    if len(term_positions) >= 2:
        positions = [pos[1] for pos in term_positions if pos[1] >= 0]
        if len(positions) >= 2:
            max_distance = max(positions) - min(positions)
            if max_distance <= 3:  # Terms within 3 words of each other
                score += 15
                print(f"Proximity bonus (≤3 words): +15")
            elif max_distance <= 5:  # Terms within 5 words
                score += 8
                print(f"Proximity bonus (≤5 words): +8")
    
    # Penalty for missing terms
    missing_terms = len(search_terms) - terms_found
    penalty = missing_terms * 15
    score -= penalty
    if penalty > 0:
        print(f"Missing terms penalty: -{penalty}")
    
    # Bonus for signal starting with first search term
    if search_terms and signal_lower.startswith(search_terms[0]):
        score += 25
        print(f"Starts with first term bonus: +25")
    
    final_score = max(0, score)
    print(f"Final score: {final_score}")
    return final_score

if __name__ == "__main__":
    debug_wildcard_search()
