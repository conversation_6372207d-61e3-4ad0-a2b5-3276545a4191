#!/usr/bin/env python3
"""
Test script to verify AXIS_PTS and other A2L object types are found
"""

import tempfile
import os

def test_axis_pts_search():
    """Test searching for AXIS_PTS objects."""
    print("TESTING AXIS_PTS AND OTHER A2L OBJECT SEARCH")
    print("=" * 50)
    
    # Sample A2L content with different object types
    sample_a2l_content = '''
/begin MEASUREMENT test_measurement
   "Test measurement signal"
   UBYTE
   _CNV_A_R_LINEAR_____3_CM
   1
   100.
   0.
   255.
   DISPLAY_IDENTIFIER TEST_MEASUREMENT
   ECU_ADDRESS 0x40005abc
/end MEASUREMENT

/begin CHARACTERISTIC test_characteristic
   "Test characteristic signal"
   VALUE
   0x80005678
   _REC_S1VAL_20_U1
   255.
   _CNV_A_R_LINEAR_____3_CM
   0.
   255.
   DISPLAY_IDENTIFIER TEST_CHARACTERISTIC
/end CHARACTERISTIC

/begin AXIS_PTS LDPM_TCO
   "Lane Departure Prevention Module - Traffic Control Object axis points"
   0x12345678
   INPUT_QUANTITY SPEED_KMH
   DEPOSIT ABSOLUTE
   MAX_DIFF 10.0
   MONOTONY MON_INCREASE
   BYTE_ORDER MSB_FIRST
   DISPLAY_IDENTIFIER LDPM_TCO_AXIS
   ECU_ADDRESS 0x40001234
/end AXIS_PTS

/begin AXIS_PTS ENGINE_SPEED_AXIS
   "Engine speed axis points for calibration"
   0x87654321
   INPUT_QUANTITY ENGINE_RPM
   DEPOSIT ABSOLUTE
   MAX_DIFF 50.0
   MONOTONY MON_INCREASE
   BYTE_ORDER MSB_FIRST
/end AXIS_PTS

/begin COMPU_METHOD SPEED_CONVERSION
   "Speed conversion method from raw to km/h"
   LINEAR
   "%6.2"
   "km/h"
   COEFFS_LINEAR 0.1 0.0
/end COMPU_METHOD

/begin COMPU_TAB GEAR_TABLE
   "Gear position lookup table"
   TAB_VERB
   12
   0 "P"
   1 "R"
   2 "N"
   3 "D1"
   4 "D2"
   5 "D3"
   6 "D4"
   7 "D5"
   8 "D6"
   9 "D7"
   10 "D8"
   11 "INVALID"
   DEFAULT_VALUE "INVALID"
/end COMPU_TAB

/begin GROUP ENGINE_CONTROL
   "Engine control parameters group"
   /begin REF_CHARACTERISTIC
      test_characteristic
      ENGINE_SPEED_AXIS
   /end REF_CHARACTERISTIC
   /begin REF_MEASUREMENT
      test_measurement
   /end REF_MEASUREMENT
/end GROUP
'''
    
    # Create temporary A2L file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.a2l', delete=False, encoding='utf-8') as f:
        f.write(sample_a2l_content)
        test_file = f.name
    
    try:
        # Import the search function
        from main import search_signal_in_a2l
        
        # Test cases for different A2L object types
        test_cases = [
            ('test_measurement', 'MEASUREMENT'),
            ('test_characteristic', 'CHARACTERISTIC'),
            ('LDPM_TCO', 'AXIS_PTS'),
            ('ENGINE_SPEED_AXIS', 'AXIS_PTS'),
            ('SPEED_CONVERSION', 'COMPU_METHOD'),
            ('GEAR_TABLE', 'COMPU_TAB'),
            ('ENGINE_CONTROL', 'GROUP'),
        ]
        
        print("Testing different A2L object types:")
        print("-" * 40)
        
        passed = 0
        total = len(test_cases)
        
        for signal_name, expected_type in test_cases:
            print(f"\nSearching for: '{signal_name}' (expected: {expected_type})")
            
            result = search_signal_in_a2l(signal_name, test_file)
            
            if result:
                print(f"  ✅ FOUND: {result['name']} ({result['type']})")
                print(f"     Description: {result['description']}")
                
                if result['type'] == expected_type:
                    print(f"     ✅ Correct type: {expected_type}")
                    passed += 1
                else:
                    print(f"     ❌ Wrong type: expected {expected_type}, got {result['type']}")
            else:
                print(f"  ❌ NOT FOUND")
        
        print(f"\n" + "-" * 40)
        print(f"Test Results: {passed}/{total} passed ({(passed/total)*100:.1f}%)")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! All A2L object types are now searchable.")
        else:
            print(f"⚠️ {total-passed} test(s) failed.")
        
        # Test the specific LDPM_TCO case
        print(f"\n" + "=" * 50)
        print("SPECIFIC TEST: LDPM_TCO (Your Original Issue)")
        print("=" * 50)
        
        result = search_signal_in_a2l('LDPM_TCO', test_file)
        if result:
            print("✅ SUCCESS! LDPM_TCO found as AXIS_PTS")
            print(f"   Name: {result['name']}")
            print(f"   Type: {result['type']}")
            print(f"   Description: {result['description']}")
            if 'address' in result and result['address']:
                print(f"   Address: {result['address']}")
        else:
            print("❌ FAILED! LDPM_TCO still not found")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure main.py is in the current directory.")
    except Exception as e:
        print(f"❌ Test error: {e}")
    finally:
        # Clean up
        if os.path.exists(test_file):
            os.unlink(test_file)
            print(f"\nCleaned up test file: {test_file}")

def test_gui_axis_pts():
    """Test the GUI with AXIS_PTS objects."""
    print(f"\n" + "=" * 50)
    print("TESTING GUI WITH AXIS_PTS")
    print("=" * 50)
    
    try:
        import tkinter as tk
        from a2l_gui import A2LSignalSearchGUI
        
        print("✅ GUI imports successful")
        print("✅ AXIS_PTS support added to GUI")
        print("✅ Auto-suggestions will now include AXIS_PTS objects")
        print("✅ Fuzzy matching will work with AXIS_PTS objects")
        
        print("\nTo test GUI:")
        print("1. Run: python a2l_gui.py")
        print("2. Load your A2L file")
        print("3. Search for: LDPM_TCO")
        print("4. Should find it as AXIS_PTS object")
        
    except ImportError as e:
        print(f"❌ GUI import error: {e}")

def show_supported_objects():
    """Show all supported A2L object types."""
    print(f"\n" + "=" * 50)
    print("SUPPORTED A2L OBJECT TYPES")
    print("=" * 50)
    
    supported_types = [
        ('MEASUREMENT', 'Measurement signals with data acquisition'),
        ('CHARACTERISTIC', 'Calibration parameters and maps'),
        ('AXIS_PTS', 'Axis point definitions for maps and curves'),
        ('COMPU_METHOD', 'Computation methods for value conversion'),
        ('COMPU_TAB', 'Lookup tables for value conversion'),
        ('COMPU_VTAB', 'Value tables with text representations'),
        ('COMPU_VTAB_RANGE', 'Value table ranges'),
        ('GROUP', 'Logical grouping of objects'),
        ('FUNCTION', 'Function definitions'),
        ('MOD_PAR', 'Module parameters'),
        ('MOD_COMMON', 'Common module information'),
    ]
    
    print("Now searchable object types:")
    for obj_type, description in supported_types:
        print(f"  ✅ {obj_type:<20} - {description}")
    
    print(f"\nTotal: {len(supported_types)} A2L object types supported")

if __name__ == "__main__":
    test_axis_pts_search()
    test_gui_axis_pts()
    show_supported_objects()
