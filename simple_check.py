#!/usr/bin/env python3
"""
Simple check for the signal
"""

import os

def simple_check():
    print("Simple Signal Check")
    print("=" * 30)
    
    # Check if A2L file exists
    a2l_file = "FS_RNCP3_3b_B40_4veh.a2l"
    if os.path.exists(a2l_file):
        print(f"✅ A2L file found: {a2l_file}")
        file_size = os.path.getsize(a2l_file)
        print(f"   File size: {file_size:,} bytes")
    else:
        print(f"❌ A2L file not found: {a2l_file}")
        return
    
    # Search for the signal
    signal_name = "LDP_TCO__IP_IGA_ST"
    print(f"\n🔍 Searching for: {signal_name}")
    
    try:
        with open(a2l_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Simple text search
        if signal_name in content:
            print("✅ Signal found in file!")
            
            # Find the line
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if signal_name in line:
                    print(f"   Line {i}: {line.strip()}")
                    
                    # Show context (few lines before and after)
                    start = max(0, i-3)
                    end = min(len(lines), i+3)
                    print("\n   Context:")
                    for j in range(start, end):
                        marker = " >>> " if j == i-1 else "     "
                        print(f"   {marker}Line {j+1}: {lines[j].strip()}")
                    break
        else:
            print("❌ Signal NOT found in file")
            
            # Try case insensitive
            if signal_name.lower() in content.lower():
                print("✅ Found with case-insensitive search")
            else:
                print("❌ Not found even with case-insensitive search")
                
                # Try searching for parts
                parts = ["LDP", "TCO", "IGA", "ST"]
                print("\n🔍 Searching for parts:")
                for part in parts:
                    if part in content:
                        print(f"   ✅ Found: {part}")
                    else:
                        print(f"   ❌ Not found: {part}")
    
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    simple_check()
