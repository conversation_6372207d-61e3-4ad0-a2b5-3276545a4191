#!/usr/bin/env python3
"""
Simple script to find your signal in the A2L file
"""

import re
import os

def find_signal_simple():
    """Simple function to find the signal."""
    a2l_file = "FS_RNCP3_3b_B40_4veh.a2l"
    signal_name = "LDP_TCO__IP_IGA_ST"
    
    print(f"Looking for signal: {signal_name}")
    print(f"In file: {a2l_file}")
    print("-" * 50)
    
    if not os.path.exists(a2l_file):
        print(f"ERROR: File {a2l_file} not found!")
        print("Please make sure the A2L file is in the same directory as this script.")
        return
    
    try:
        with open(a2l_file, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        print(f"File loaded: {len(lines)} lines")
        
        # Search for any line containing the signal name
        found_lines = []
        for i, line in enumerate(lines, 1):
            if signal_name.lower() in line.lower():
                found_lines.append((i, line.strip()))
        
        if found_lines:
            print(f"\nFound {len(found_lines)} lines containing '{signal_name}':")
            for line_num, line_content in found_lines:
                print(f"Line {line_num}: {line_content}")
        else:
            print(f"\nNo lines found containing '{signal_name}'")
            
            # Try searching for parts of the signal name
            parts = signal_name.split('_')
            print(f"\nTrying to find parts of the signal name:")
            for part in parts:
                if len(part) >= 3:
                    part_lines = []
                    for i, line in enumerate(lines, 1):
                        if part.lower() in line.lower() and 'begin' in line.lower():
                            part_lines.append((i, line.strip()))
                    
                    if part_lines:
                        print(f"\nFound lines containing '{part}':")
                        for line_num, line_content in part_lines[:5]:  # Show first 5
                            print(f"  Line {line_num}: {line_content}")
        
        # Also search for signals with double underscores
        print(f"\nSearching for any signals with double underscores (__):")
        double_underscore_signals = []
        
        for i, line in enumerate(lines, 1):
            if re.match(r'/begin\s+(MEASUREMENT|CHARACTERISTIC)\s+.*__.*', line.strip(), re.IGNORECASE):
                double_underscore_signals.append((i, line.strip()))
        
        if double_underscore_signals:
            print(f"Found {len(double_underscore_signals)} signals with double underscores:")
            for line_num, line_content in double_underscore_signals[:10]:  # Show first 10
                print(f"  Line {line_num}: {line_content}")
        else:
            print("No signals with double underscores found")
            
    except Exception as e:
        print(f"ERROR reading file: {e}")

if __name__ == "__main__":
    find_signal_simple()
