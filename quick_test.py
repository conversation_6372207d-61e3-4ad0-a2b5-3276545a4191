#!/usr/bin/env python3
"""
Quick test to verify the A2L search functionality
"""

import re
import os

def quick_test():
    """Quick test of A2L search functionality."""
    a2l_file = "FS_RNCP3_3b_B40_4veh.a2l"
    
    if not os.path.exists(a2l_file):
        print(f"Error: A2L file '{a2l_file}' not found.")
        return
    
    print("Quick test of A2L search functionality")
    print("=" * 50)
    
    # Test signal to search for
    test_signal = "eng_hour_ofs"
    
    print(f"Searching for signal: {test_signal}")
    
    try:
        found = False
        with open(a2l_file, 'r', encoding='utf-8', errors='ignore') as file:
            line_count = 0
            in_measurement = False
            measurement_content = []
            
            for line in file:
                line_count += 1
                line = line.strip()
                
                # Progress indicator
                if line_count % 10000 == 0:
                    print(f"Processed {line_count} lines...")
                
                # Check for our target signal
                if re.match(rf'/begin\s+MEASUREMENT\s+{re.escape(test_signal)}\s*', line, re.IGNORECASE):
                    print(f"Found MEASUREMENT signal '{test_signal}' at line {line_count}")
                    in_measurement = True
                    measurement_content = []
                    continue
                
                # Collect content
                if in_measurement:
                    if re.match(r'/end\s+MEASUREMENT', line, re.IGNORECASE):
                        # End of measurement, process content
                        content = '\n'.join(measurement_content)
                        
                        print("\nSignal Details:")
                        print("-" * 30)
                        
                        # Extract description
                        desc_match = re.search(r'"([^"]*)"', content)
                        if desc_match:
                            print(f"Description: {desc_match.group(1)}")
                        
                        # Extract data type
                        data_type_match = re.search(r'(UBYTE|SBYTE|UWORD|SWORD|ULONG|SLONG|FLOAT32_IEEE)', content)
                        if data_type_match:
                            print(f"Data Type: {data_type_match.group(1)}")
                        
                        # Extract ECU address
                        ecu_addr_match = re.search(r'ECU_ADDRESS\s+(0x[0-9a-fA-F]+)', content)
                        if ecu_addr_match:
                            print(f"ECU Address: {ecu_addr_match.group(1)}")
                        
                        # Extract display identifier
                        display_id_match = re.search(r'DISPLAY_IDENTIFIER\s+(\w+)', content)
                        if display_id_match:
                            print(f"Display Identifier: {display_id_match.group(1)}")
                        
                        # Extract format
                        format_match = re.search(r'FORMAT\s+"([^"]*)"', content)
                        if format_match:
                            print(f"Format: {format_match.group(1)}")
                        
                        print("\nRaw Content (first 200 chars):")
                        print(content[:200] + ("..." if len(content) > 200 else ""))
                        
                        found = True
                        break
                    else:
                        measurement_content.append(line)
                
                # Stop after reasonable number of lines for testing
                if line_count > 100000:
                    print("Stopping search after 100,000 lines for testing...")
                    break
        
        if not found:
            print(f"Signal '{test_signal}' not found in the first 100,000 lines.")
        
        print(f"\nTotal lines processed: {line_count}")
        
    except Exception as e:
        print(f"Error during search: {e}")

if __name__ == "__main__":
    quick_test()
