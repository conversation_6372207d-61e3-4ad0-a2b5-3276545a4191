#!/usr/bin/env python3
"""
Test script for CHARACTERISTIC sub-types and enhanced filtering
"""

import tempfile
import os

def test_characteristic_subtypes():
    """Test CHARACTERISTIC sub-type detection and filtering."""
    print("TESTING CHARACTERISTIC SUB-TYPES")
    print("=" * 50)
    
    # Sample A2L content with different CHARACTERISTIC types
    sample_a2l_content = '''
/begin MEASUREMENT engine_speed
   "Engine speed measurement"
   UWORD
   _CNV_A_R_LINEAR_____0_CM
   1
   100.
   0.
   8000.
   DISPLAY_IDENTIFIER ENGINE_SPEED_RPM
   ECU_ADDRESS 0x40005abc
/end MEASUREMENT

/begin CHARACTERISTIC engine_torque_map
   "Engine torque map calibration"
   MAP
   0x80001234
   _REC_S2VAL_20_U1
   255.
   _CNV_A_R_LINEAR_____3_CM
   0.
   255.
   DISPLAY_IDENTIFIER ENGINE_TORQUE_MAP
   /begin AXIS_DESCR
      COM_AXIS
      INPUT_QUANTITY ENGINE_SPEED
      CONVERSION _CNV_A_R_LINEAR_____0_CM
      16
      0.0
      8000.0
   /end AXIS_DESCR
   /begin AXIS_DESCR
      COM_AXIS
      INPUT_QUANTITY ENGINE_LOAD
      CONVERSION _CNV_A_R_LINEAR_____1_CM
      16
      0.0
      100.0
   /end AXIS_DESCR
/end CHARACTERISTIC

/begin CHARACTERISTIC max_engine_speed
   "Maximum engine speed limit"
   VALUE
   0x80002345
   _REC_S1VAL_20_U1
   8000.
   _CNV_A_R_LINEAR_____0_CM
   0.
   10000.
   DISPLAY_IDENTIFIER MAX_ENGINE_SPEED
/end CHARACTERISTIC

/begin CHARACTERISTIC fuel_injection_curve
   "Fuel injection timing curve"
   CURVE
   0x80003456
   _REC_S1VAL_20_U1
   255.
   _CNV_A_R_LINEAR_____2_CM
   0.
   255.
   DISPLAY_IDENTIFIER FUEL_INJECTION_CURVE
   /begin AXIS_DESCR
      COM_AXIS
      INPUT_QUANTITY ENGINE_LOAD
      CONVERSION _CNV_A_R_LINEAR_____1_CM
      16
      0.0
      100.0
   /end AXIS_DESCR
/end CHARACTERISTIC

/begin AXIS_PTS engine_speed_axis
   "Engine speed axis points"
   0x12345678
   INPUT_QUANTITY ENGINE_SPEED
   DEPOSIT ABSOLUTE
   MAX_DIFF 50.0
   MONOTONY MON_INCREASE
   BYTE_ORDER MSB_FIRST
   DISPLAY_IDENTIFIER ENGINE_SPEED_AXIS
/end AXIS_PTS

/begin COMPU_METHOD speed_conversion
   "Speed conversion method"
   LINEAR
   "%6.2"
   "rpm"
   COEFFS_LINEAR 1.0 0.0
/end COMPU_METHOD
'''
    
    # Create temporary A2L file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.a2l', delete=False, encoding='utf-8') as f:
        f.write(sample_a2l_content)
        test_file = f.name
    
    try:
        print("Sample A2L content created with:")
        print("  • 1 MEASUREMENT signal")
        print("  • 1 CHARACTERISTIC-MAP (engine_torque_map)")
        print("  • 1 CHARACTERISTIC-VAL (max_engine_speed)")
        print("  • 1 CHARACTERISTIC-CUR (fuel_injection_curve)")
        print("  • 1 AXIS_PTS object")
        print("  • 1 COMPU_METHOD object")
        
        print(f"\nTesting GUI indexing functionality...")
        
        # Test the indexing method
        try:
            import tkinter as tk
            from a2l_gui import A2LSignalSearchGUI
            
            # Create a hidden root window for testing
            root = tk.Tk()
            root.withdraw()
            
            # Create GUI instance
            app = A2LSignalSearchGUI(root)
            
            # Read the test file content
            with open(test_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Test the indexing method
            app._index_a2l_objects(content)
            
            print(f"\n✅ Indexing completed successfully!")
            print(f"   Total signals indexed: {len(app.all_signals)}")
            
            # Show the indexed signals with their types
            print(f"\nIndexed signals and their types:")
            print("-" * 40)
            
            for signal_name in sorted(app.all_signals):
                signal_info = app.signal_index[signal_name.lower()]
                signal_type = signal_info['type']
                full_type = signal_info.get('full_type', signal_type)
                print(f"  {signal_name:<25} → {full_type}")
            
            # Test filter functionality
            print(f"\nTesting filter functionality:")
            print("-" * 30)
            
            filter_tests = [
                ('MEASUREMENT', 'filter_measurement'),
                ('CHAR_MAP', 'filter_char_map'),
                ('CHAR_VAL', 'filter_char_val'),
                ('CHAR_CUR', 'filter_char_cur'),
                ('AXIS_PTS', 'filter_axis_pts'),
                ('OTHER', 'filter_other')
            ]
            
            for signal_type, filter_attr in filter_tests:
                # Count signals of this type
                count = sum(1 for signal in app.all_signals 
                           if app.signal_index[signal.lower()]['type'] == signal_type)
                
                if count > 0:
                    print(f"  ✅ {signal_type:<12} → {count} signal(s) found")
                else:
                    print(f"  ⚪ {signal_type:<12} → No signals in test data")
            
            # Clean up
            root.destroy()
            
        except ImportError as e:
            print(f"❌ GUI import error: {e}")
        except Exception as e:
            print(f"❌ Test error: {e}")
        
    finally:
        # Clean up test file
        if os.path.exists(test_file):
            os.unlink(test_file)

def demonstrate_new_features():
    """Demonstrate the new filtering features."""
    print(f"\n" + "=" * 50)
    print("NEW FILTERING FEATURES")
    print("=" * 50)
    
    features = [
        {
            'title': 'CHARACTERISTIC Sub-Type Detection',
            'description': 'Automatically detects MAP, VAL, and CUR types',
            'details': [
                '• MAP: Multi-dimensional calibration maps',
                '• VAL: Scalar values (single parameters)',
                '• CUR: Curves (1D calibration tables)'
            ]
        },
        {
            'title': 'Enhanced Filter Checkboxes',
            'description': 'Separate filters for each CHARACTERISTIC sub-type',
            'details': [
                '• MEASUREMENT: All measurement signals',
                '• MAP (Maps): Multi-dimensional characteristics',
                '• VAL (Scalars): Single-value characteristics',
                '• CUR (Curves): 1D curve characteristics',
                '• AXIS_PTS: Axis point definitions',
                '• OTHER: All other A2L objects'
            ]
        },
        {
            'title': 'Select All Functionality',
            'description': 'Convenient checkbox to toggle all filters',
            'details': [
                '• Check "SELECT ALL" → Enables all filters',
                '• Uncheck "SELECT ALL" → Disables all filters',
                '• Auto-updates based on individual filter states',
                '• Smart state management'
            ]
        },
        {
            'title': 'Enhanced Suggestions Display',
            'description': 'Shows detailed type information',
            'details': [
                '• engine_torque_map (CHARACTERISTIC-MAP)',
                '• max_engine_speed (CHARACTERISTIC-VAL)',
                '• fuel_curve (CHARACTERISTIC-CUR)',
                '• engine_speed (MEASUREMENT)',
                '• speed_axis (AXIS_PTS)'
            ]
        }
    ]
    
    for feature in features:
        print(f"\n{feature['title']}:")
        print("-" * len(feature['title']))
        print(f"{feature['description']}")
        for detail in feature['details']:
            print(f"  {detail}")

def show_gui_layout():
    """Show the updated GUI layout."""
    print(f"\n" + "=" * 50)
    print("UPDATED GUI LAYOUT")
    print("=" * 50)
    
    layout = """
    ┌─────────────────────────────────────────────────────────┐
    │                    A2L Signal Search                    │
    ├─────────────────────────────────────────────────────────┤
    │ File Selection                                          │
    │ [Browse...] FS_RNCP3_3b_B40_4veh.a2l                  │
    ├─────────────────────────────────────────────────────────┤
    │ Filter by Object Type                                   │
    │ ☑ SELECT ALL                                           │
    │                                                         │
    │ ☑ MEASUREMENT    ☑ AXIS_PTS    ☑ OTHER                │
    │                                                         │
    │ CHARACTERISTIC Types:                                   │
    │ ☑ MAP (Maps)    ☑ VAL (Scalars)    ☑ CUR (Curves)    │
    ├─────────────────────────────────────────────────────────┤
    │ Signal Search                                           │
    │ Signal Name: [*ENGINE*MAP*_______] [Search]            │
    │ 💡 Tip: Use * for wildcard search                      │
    │                                                         │
    │ ┌─ Suggestions ─────────────────────────────────────┐   │
    │ │ ▶ engine_torque_map (CHARACTERISTIC-MAP)        │   │
    │ │   fuel_injection_map (CHARACTERISTIC-MAP)       │   │
    │ │   transmission_map (CHARACTERISTIC-MAP)         │   │
    │ └─────────────────────────────────────────────────┘   │
    └─────────────────────────────────────────────────────────┘
    """
    
    print(layout)

def usage_examples():
    """Show usage examples for the new features."""
    print(f"\n" + "=" * 50)
    print("USAGE EXAMPLES")
    print("=" * 50)
    
    examples = [
        {
            'scenario': 'Find Only Calibration Maps',
            'steps': [
                '1. Uncheck "SELECT ALL"',
                '2. Check only "MAP (Maps)"',
                '3. Type "*ENGINE*" or "*TORQUE*"',
                '4. See only MAP-type characteristics'
            ]
        },
        {
            'scenario': 'Find Scalar Parameters Only',
            'steps': [
                '1. Uncheck all except "VAL (Scalars)"',
                '2. Type "*LIMIT*" or "*MAX*"',
                '3. See only scalar characteristics',
                '4. Navigate with keyboard'
            ]
        },
        {
            'scenario': 'Find All CHARACTERISTIC Types',
            'steps': [
                '1. Uncheck MEASUREMENT, AXIS_PTS, OTHER',
                '2. Keep MAP, VAL, CUR checked',
                '3. Type "*ENGINE*"',
                '4. See all characteristic sub-types'
            ]
        },
        {
            'scenario': 'Quick Toggle All Filters',
            'steps': [
                '1. Use "SELECT ALL" to enable/disable all',
                '2. Fine-tune individual filters as needed',
                '3. Search with current filter settings',
                '4. Filters persist across searches'
            ]
        }
    ]
    
    for example in examples:
        print(f"\n{example['scenario']}:")
        print("-" * len(example['scenario']))
        for step in example['steps']:
            print(f"  {step}")

if __name__ == "__main__":
    test_characteristic_subtypes()
    demonstrate_new_features()
    show_gui_layout()
    usage_examples()
