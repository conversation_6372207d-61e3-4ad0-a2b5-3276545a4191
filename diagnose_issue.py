#!/usr/bin/env python3
"""
Diagnose the signal search issue
"""

import re
import os

def diagnose_signal_issue():
    """Diagnose why the signal is not being found."""
    print("A2L Signal Search Diagnostic")
    print("=" * 40)
    
    # Check if files exist
    files_to_check = [
        "FS_RNCP3_3b_B40_4veh.a2l",
        "main.py",
        "a2l_gui.py"
    ]
    
    print("1. Checking files:")
    for file_name in files_to_check:
        if os.path.exists(file_name):
            size = os.path.getsize(file_name)
            print(f"   ✅ {file_name} ({size:,} bytes)")
        else:
            print(f"   ❌ {file_name} - NOT FOUND")
    
    print()
    
    # Test the regex pattern
    print("2. Testing regex patterns:")
    test_lines = [
        "/begin MEASUREMENT LDP_TCO__IP_IGA_ST",
        "/begin MEASUREMENT normal_signal",
        "/begin CHARACTERISTIC LDP_TCO__IP_IGA_ST"
    ]
    
    old_pattern = r'/begin\s+MEASUREMENT\s+(\w+)'
    new_pattern = r'/begin\s+MEASUREMENT\s+([A-Za-z0-9_]+)'
    
    for test_line in test_lines:
        print(f"   Testing: {test_line}")
        
        old_match = re.match(old_pattern, test_line, re.IGNORECASE)
        new_match = re.match(new_pattern, test_line, re.IGNORECASE)
        
        print(f"     Old pattern: {'✅' if old_match else '❌'}")
        print(f"     New pattern: {'✅' if new_match else '❌'}")
        if new_match:
            print(f"     Captured: '{new_match.group(1)}'")
        print()
    
    # Check A2L file content
    a2l_file = "FS_RNCP3_3b_B40_4veh.a2l"
    if os.path.exists(a2l_file):
        print("3. Checking A2L file content:")
        try:
            with open(a2l_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            print(f"   File size: {len(content):,} characters")
            
            # Count MEASUREMENT and CHARACTERISTIC blocks
            measurement_count = len(re.findall(r'/begin\s+MEASUREMENT', content, re.IGNORECASE))
            characteristic_count = len(re.findall(r'/begin\s+CHARACTERISTIC', content, re.IGNORECASE))
            
            print(f"   MEASUREMENT blocks: {measurement_count}")
            print(f"   CHARACTERISTIC blocks: {characteristic_count}")
            
            # Search for the specific signal
            signal_name = "LDP_TCO__IP_IGA_ST"
            if signal_name in content:
                print(f"   ✅ Signal '{signal_name}' found in file content")
                
                # Find the line number
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if signal_name in line:
                        print(f"   Line {i}: {line.strip()}")
                        break
            else:
                print(f"   ❌ Signal '{signal_name}' NOT found in file content")
                
                # Try case-insensitive search
                if signal_name.lower() in content.lower():
                    print(f"   ✅ Signal found with case-insensitive search")
                else:
                    print(f"   ❌ Signal not found even with case-insensitive search")
            
        except Exception as e:
            print(f"   ❌ Error reading A2L file: {e}")
    
    print()
    
    # Test the actual search function
    print("4. Testing search function:")
    try:
        # Import and test the search function
        import sys
        sys.path.append('.')
        
        from main import search_signal_in_a2l
        
        signal_name = "LDP_TCO__IP_IGA_ST"
        result = search_signal_in_a2l(signal_name, a2l_file)
        
        if result:
            print(f"   ✅ Search function found the signal!")
            print(f"   Signal: {result['name']}")
            print(f"   Type: {result['type']}")
            print(f"   Description: {result['description']}")
        else:
            print(f"   ❌ Search function did NOT find the signal")
            
            # Try with different cases
            for variation in [signal_name.upper(), signal_name.lower()]:
                result = search_signal_in_a2l(variation, a2l_file)
                if result:
                    print(f"   ✅ Found with variation: {variation}")
                    break
            else:
                print(f"   ❌ No variations worked either")
        
    except ImportError as e:
        print(f"   ❌ Could not import search function: {e}")
    except Exception as e:
        print(f"   ❌ Error testing search function: {e}")
    
    print()
    print("=" * 40)
    print("DIAGNOSTIC COMPLETE")
    print("=" * 40)

if __name__ == "__main__":
    diagnose_signal_issue()
