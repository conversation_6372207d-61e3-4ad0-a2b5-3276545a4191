#!/usr/bin/env python3
"""
A2L Signal Search Application

This application searches for signal details in A2L files using an efficient
streaming approach to handle large files. It supports case-insensitive search
for signal names and provides comprehensive information about the found signals.

Usage:
    python main.py [signal_name] [a2l_file_path]

If no arguments are provided, the application will prompt for input.
"""

import sys
import re
import os
from typing import Dict, Optional


def search_signal_in_a2l(signal_name: str, file_path: str) -> Optional[Dict]:
    """Search for a specific signal in the A2L file efficiently."""
    signal_name_lower = signal_name.lower()

    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            current_section = None
            current_signal = None
            signal_content = []
            in_target_signal = False
            line_count = 0

            for line in file:
                line_count += 1
                line = line.strip()

                # Progress indicator for large files
                if line_count % 50000 == 0:
                    print(f"Processed {line_count} lines...")

                # Check for beginning of MEASUREMENT or CHARACTERISTIC
                measurement_match = re.match(r'/begin\s+MEASUREMENT\s+(\w+)', line, re.IGNORECASE)
                characteristic_match = re.match(r'/begin\s+CHARACTERISTIC\s+(\w+)', line, re.IGNORECASE)

                if measurement_match:
                    current_section = 'MEASUREMENT'
                    current_signal = measurement_match.group(1)
                    signal_content = []
                    in_target_signal = (current_signal.lower() == signal_name_lower)
                    continue

                elif characteristic_match:
                    current_section = 'CHARACTERISTIC'
                    current_signal = characteristic_match.group(1)
                    signal_content = []
                    in_target_signal = (current_signal.lower() == signal_name_lower)
                    continue

                # Check for end of section
                if re.match(r'/end\s+(MEASUREMENT|CHARACTERISTIC)', line, re.IGNORECASE):
                    if in_target_signal and current_signal:
                        # Found our target signal, extract information
                        content_str = '\n'.join(signal_content)
                        if current_section == 'MEASUREMENT':
                            return extract_measurement_info(current_signal, content_str)
                        else:
                            return extract_characteristic_info(current_signal, content_str)

                    current_section = None
                    current_signal = None
                    signal_content = []
                    in_target_signal = False
                    continue

                # Collect content if we're in the target signal
                if in_target_signal:
                    signal_content.append(line)

        return None

    except Exception as e:
        print(f"Error reading file: {e}")
        return None


def extract_measurement_info(signal_name: str, content: str) -> Dict:
    """Extract information from a MEASUREMENT block."""
    info = {
        'name': signal_name,
        'type': 'MEASUREMENT',
        'description': '',
        'data_type': '',
        'conversion': '',
        'resolution': '',
        'accuracy': '',
        'lower_limit': '',
        'upper_limit': '',
        'display_identifier': '',
        'ecu_address': '',
        'format': '',
        'raw_content': content.strip()
    }

    # Extract description (usually the first quoted string)
    desc_match = re.search(r'"([^"]*)"', content)
    if desc_match:
        info['description'] = desc_match.group(1)

    # Extract data type
    data_type_match = re.search(r'(UBYTE|SBYTE|UWORD|SWORD|ULONG|SLONG|FLOAT32_IEEE)', content)
    if data_type_match:
        info['data_type'] = data_type_match.group(1)

    # Extract common fields
    extract_common_fields(info, content)

    return info


def extract_characteristic_info(signal_name: str, content: str) -> Dict:
    """Extract information from a CHARACTERISTIC block."""
    info = {
        'name': signal_name,
        'type': 'CHARACTERISTIC',
        'description': '',
        'characteristic_type': '',
        'address': '',
        'conversion': '',
        'lower_limit': '',
        'upper_limit': '',
        'display_identifier': '',
        'format': '',
        'bit_mask': '',
        'raw_content': content.strip()
    }

    # Extract description (usually the first quoted string)
    desc_match = re.search(r'"([^"]*)"', content)
    if desc_match:
        info['description'] = desc_match.group(1)

    # Extract characteristic type
    char_type_match = re.search(r'(VALUE|CURVE|MAP|CUBOID|CUBE_4|CUBE_5)', content)
    if char_type_match:
        info['characteristic_type'] = char_type_match.group(1)

    # Extract address (hex value)
    addr_match = re.search(r'0x[0-9a-fA-F]+', content)
    if addr_match:
        info['address'] = addr_match.group(0)

    # Extract bit mask if present
    bit_mask_match = re.search(r'BIT_MASK\s+(0x[0-9a-fA-F]+)', content)
    if bit_mask_match:
        info['bit_mask'] = bit_mask_match.group(1)

    # Extract common fields
    extract_common_fields(info, content)

    return info


def extract_common_fields(info: Dict, content: str):
    """Extract common fields present in both MEASUREMENT and CHARACTERISTIC."""
    # Extract DISPLAY_IDENTIFIER
    display_id_match = re.search(r'DISPLAY_IDENTIFIER\s+(\w+)', content)
    if display_id_match:
        info['display_identifier'] = display_id_match.group(1)

    # Extract ECU_ADDRESS
    ecu_addr_match = re.search(r'ECU_ADDRESS\s+(0x[0-9a-fA-F]+)', content)
    if ecu_addr_match:
        info['ecu_address'] = ecu_addr_match.group(1)

    # Extract FORMAT
    format_match = re.search(r'FORMAT\s+"([^"]*)"', content)
    if format_match:
        info['format'] = format_match.group(1)

    # Extract conversion method
    conv_match = re.search(r'_CNV_[A-Z0-9_]+', content)
    if conv_match:
        info['conversion'] = conv_match.group(0)

    # Extract numeric values (limits, resolution, etc.)
    numbers = re.findall(r'\b\d+\.?\d*\b', content)
    if len(numbers) >= 3:
        info['resolution'] = numbers[0] if len(numbers) > 0 else ''
        info['accuracy'] = numbers[1] if len(numbers) > 1 else ''
        info['lower_limit'] = numbers[2] if len(numbers) > 2 else ''
        info['upper_limit'] = numbers[3] if len(numbers) > 3 else ''


def find_similar_signals(signal_name: str, file_path: str, max_results: int = 10) -> list:
    """Find signals with similar names using streaming approach."""
    similar_signals = []
    signal_name_lower = signal_name.lower()

    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            line_count = 0
            for line in file:
                line_count += 1
                line = line.strip()

                # Progress indicator
                if line_count % 50000 == 0:
                    print(f"Searched {line_count} lines...")

                # Check for signal names
                measurement_match = re.match(r'/begin\s+MEASUREMENT\s+(\w+)', line, re.IGNORECASE)
                characteristic_match = re.match(r'/begin\s+CHARACTERISTIC\s+(\w+)', line, re.IGNORECASE)

                if measurement_match:
                    name = measurement_match.group(1)
                    if signal_name_lower in name.lower() and name.lower() != signal_name_lower:
                        similar_signals.append(name)
                        if len(similar_signals) >= max_results:
                            break

                elif characteristic_match:
                    name = characteristic_match.group(1)
                    if signal_name_lower in name.lower() and name.lower() != signal_name_lower:
                        similar_signals.append(name)
                        if len(similar_signals) >= max_results:
                            break

        return similar_signals

    except Exception as e:
        print(f"Error searching for similar signals: {e}")
        return []


def print_signal_details(signal_info: Dict):
    """Print detailed information about a signal."""
    print("\n" + "="*80)
    print(f"SIGNAL DETAILS: {signal_info['name']}")
    print("="*80)

    print(f"Type: {signal_info['type']}")
    print(f"Description: {signal_info['description']}")

    if signal_info['type'] == 'MEASUREMENT':
        if signal_info['data_type']:
            print(f"Data Type: {signal_info['data_type']}")
        if signal_info['ecu_address']:
            print(f"ECU Address: {signal_info['ecu_address']}")

    elif signal_info['type'] == 'CHARACTERISTIC':
        if signal_info['characteristic_type']:
            print(f"Characteristic Type: {signal_info['characteristic_type']}")
        if signal_info['address']:
            print(f"Address: {signal_info['address']}")
        if signal_info['bit_mask']:
            print(f"Bit Mask: {signal_info['bit_mask']}")

    if signal_info['display_identifier']:
        print(f"Display Identifier: {signal_info['display_identifier']}")

    if signal_info['conversion']:
        print(f"Conversion Method: {signal_info['conversion']}")

    # Commented out for future use
    # if signal_info['format']:
    #     print(f"Format: {signal_info['format']}")

    if signal_info['lower_limit'] and signal_info['upper_limit']:
        print(f"Range: {signal_info['lower_limit']} to {signal_info['upper_limit']}")

    # Commented out for future use
    # if signal_info['resolution']:
    #     print(f"Resolution: {signal_info['resolution']}")

    # if signal_info['accuracy']:
    #     print(f"Accuracy: {signal_info['accuracy']}")

    print("="*80)


def main():
    """Main function to handle user input and search for signals."""
    # Default A2L file path
    default_a2l_file = "FS_RNCP3_3b_B40_4veh.a2l"

    # Get command line arguments or prompt user
    if len(sys.argv) >= 2:
        signal_name = sys.argv[1]
        a2l_file = sys.argv[2] if len(sys.argv) >= 3 else default_a2l_file
    else:
        # Prompt for signal name
        signal_name = input("Enter signal name to search: ").strip()
        if not signal_name:
            print("Error: Signal name cannot be empty.")
            return

        # Prompt for A2L file path
        a2l_file = input(f"Enter A2L file path (default: {default_a2l_file}): ").strip()
        if not a2l_file:
            a2l_file = default_a2l_file

    # Check if file exists
    if not os.path.exists(a2l_file):
        print(f"Error: A2L file '{a2l_file}' not found.")
        return

    print(f"Searching for signal '{signal_name}' in '{a2l_file}'...")
    print("Using efficient streaming search...")

    # Search for the signal
    signal_info = search_signal_in_a2l(signal_name, a2l_file)

    if signal_info:
        print_signal_details(signal_info)
    else:
        print(f"\nSignal '{signal_name}' not found.")

        # Try to find similar signals
        print("Searching for similar signals...")
        similar_signals = find_similar_signals(signal_name, a2l_file)

        if similar_signals:
            print(f"\nFound {len(similar_signals)} similar signal(s):")
            for i, sig in enumerate(similar_signals, 1):
                print(f"{i}. {sig}")
        else:
            print("No similar signals found.")


if __name__ == "__main__":
    main()