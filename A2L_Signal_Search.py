#!/usr/bin/env python3
"""
A2L Signal Search - Main Launcher

This is the main launcher for the A2L Signal Search application.
It provides a simple way to start the GUI application.
"""

import sys
import os

def main():
    """Main launcher function."""
    print("A2L Signal Search Application")
    print("=" * 30)
    print("Starting GUI application...")
    
    try:
        # Import and run the GUI
        from a2l_gui import main as gui_main
        gui_main()
        
    except ImportError as e:
        print(f"Error: Could not import GUI module: {e}")
        print("Make sure a2l_gui.py is in the same directory.")
        input("Press Enter to exit...")
        
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
