#!/usr/bin/env python3
"""
Simple test to verify basic functionality
"""

import os
import sys

def test_basic():
    """Test basic functionality."""
    print("Testing basic functionality...")
    
    # Check if A2L file exists
    a2l_file = "FS_RNCP3_3b_B40_4veh.a2l"
    
    if os.path.exists(a2l_file):
        print(f"✓ A2L file '{a2l_file}' exists")
        
        # Get file size
        file_size = os.path.getsize(a2l_file)
        print(f"✓ File size: {file_size:,} bytes ({file_size / (1024*1024):.1f} MB)")
        
        # Try to read first few lines
        try:
            with open(a2l_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = []
                for i, line in enumerate(f):
                    lines.append(line.strip())
                    if i >= 10:  # Read only first 10 lines
                        break
            
            print("✓ Successfully read first 10 lines:")
            for i, line in enumerate(lines, 1):
                print(f"  {i}: {line[:80]}{'...' if len(line) > 80 else ''}")
        
        except Exception as e:
            print(f"✗ Error reading file: {e}")
    
    else:
        print(f"✗ A2L file '{a2l_file}' not found")
    
    print("\nTest completed!")

if __name__ == "__main__":
    test_basic()
