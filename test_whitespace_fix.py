#!/usr/bin/env python3
"""
Test the whitespace fix for signal search
"""

import re

def test_whitespace_patterns():
    """Test different whitespace scenarios."""
    print("TESTING WHITESPACE PATTERNS")
    print("=" * 40)
    
    # Test cases with different whitespace scenarios
    test_lines = [
        "/begin MEASUREMENT LDP_TCO__IP_IGA_ST",           # Normal
        "/begin MEASUREMENT LDP_TCO__IP_IGA_ST ",          # Trailing space
        "/begin MEASUREMENT LDP_TCO__IP_IGA_ST\t",         # Trailing tab
        "/begin MEASUREMENT LDP_TCO__IP_IGA_ST\r",         # Trailing carriage return
        "/begin MEASUREMENT LDP_TCO__IP_IGA_ST   ",        # Multiple trailing spaces
        "/begin MEASUREMENT  LDP_TCO__IP_IGA_ST",          # Extra space before signal
        "/begin\tMEASUREMENT\tLDP_TCO__IP_IGA_ST",         # Tabs instead of spaces
        "/begin MEASUREMENT LDP_TCO__IP_IGA_ST some_extra", # Extra text after signal
    ]
    
    patterns = [
        (r'/begin\s+MEASUREMENT\s+([A-Za-z0-9_]+)', "Old pattern ([A-Za-z0-9_]+)"),
        (r'/begin\s+MEASUREMENT\s+([^\s]+)', "New pattern ([^\\s]+)"),
    ]
    
    target_signal = "LDP_TCO__IP_IGA_ST"
    
    for i, test_line in enumerate(test_lines, 1):
        print(f"\nTest {i}: '{test_line}'")
        print(f"  Length: {len(test_line)} characters")
        
        # Show special characters
        special_chars = []
        for j, char in enumerate(test_line):
            if char in [' ', '\t', '\r', '\n']:
                if char == ' ':
                    special_chars.append(f"[{j}]:SPACE")
                elif char == '\t':
                    special_chars.append(f"[{j}]:TAB")
                elif char == '\r':
                    special_chars.append(f"[{j}]:CR")
                elif char == '\n':
                    special_chars.append(f"[{j}]:LF")
        
        if special_chars:
            print(f"  Special chars: {', '.join(special_chars)}")
        
        for pattern, description in patterns:
            match = re.match(pattern, test_line, re.IGNORECASE)
            if match:
                captured = match.group(1)
                stripped = captured.strip()
                matches_target = (stripped.lower() == target_signal.lower())
                
                print(f"  ✅ {description}:")
                print(f"     Captured: '{captured}' (len={len(captured)})")
                print(f"     Stripped: '{stripped}' (len={len(stripped)})")
                print(f"     Matches target: {'✅' if matches_target else '❌'}")
            else:
                print(f"  ❌ {description}: No match")

def test_actual_search():
    """Test the actual search function with the fix."""
    print(f"\n" + "=" * 40)
    print("TESTING ACTUAL SEARCH FUNCTION")
    print("=" * 40)
    
    try:
        from main import search_signal_in_a2l
        
        signal_name = "LDP_TCO__IP_IGA_ST"
        a2l_file = "FS_RNCP3_3b_B40_4veh.a2l"
        
        print(f"Testing search for: '{signal_name}'")
        print(f"In file: {a2l_file}")
        
        result = search_signal_in_a2l(signal_name, a2l_file)
        
        if result:
            print("✅ SEARCH SUCCESSFUL!")
            print(f"   Found signal: {result['name']}")
            print(f"   Type: {result['type']}")
            print(f"   Description: {result['description']}")
        else:
            print("❌ Search still failed")
            print("   The signal might not exist in the file")
            print("   Or there might be other formatting issues")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_whitespace_patterns()
    test_actual_search()
