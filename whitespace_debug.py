#!/usr/bin/env python3
"""
Debug script to identify whitespace issues with signal names
"""

import re
import os

def debug_whitespace_issues():
    """Debug whitespace issues in signal names."""
    print("WHITESPACE DEBUG FOR A2L SIGNALS")
    print("=" * 40)
    
    a2l_file = "FS_RNCP3_3b_B40_4veh.a2l"
    target_signal = "LDP_TCO__IP_IGA_ST"
    
    if not os.path.exists(a2l_file):
        print(f"❌ File not found: {a2l_file}")
        return
    
    try:
        with open(a2l_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        lines = content.split('\n')
        print(f"✅ File loaded: {len(lines):,} lines")
        
        # Search for lines containing the signal name (case insensitive)
        print(f"\n🔍 Searching for lines containing: '{target_signal}'")
        
        matching_lines = []
        for i, line in enumerate(lines, 1):
            if target_signal.lower() in line.lower():
                matching_lines.append((i, line))
        
        if matching_lines:
            print(f"✅ Found {len(matching_lines)} line(s) containing the signal:")
            
            for line_num, line_content in matching_lines:
                print(f"\nLine {line_num}:")
                print(f"  Raw line: '{line_content}'")
                print(f"  Stripped: '{line_content.strip()}'")
                print(f"  Length: {len(line_content)} characters")
                
                # Show character-by-character breakdown
                print("  Character breakdown:")
                for j, char in enumerate(line_content):
                    if char == ' ':
                        print(f"    [{j}]: SPACE")
                    elif char == '\t':
                        print(f"    [{j}]: TAB")
                    elif char == '\r':
                        print(f"    [{j}]: CARRIAGE_RETURN")
                    elif ord(char) < 32:
                        print(f"    [{j}]: CONTROL_CHAR({ord(char)})")
                    else:
                        print(f"    [{j}]: '{char}'")
                
                # Test different regex patterns on this line
                print("  Regex pattern tests:")
                
                patterns = [
                    (r'/begin\s+MEASUREMENT\s+(\w+)', "Original pattern (\\w+)"),
                    (r'/begin\s+MEASUREMENT\s+([A-Za-z0-9_]+)', "Fixed pattern ([A-Za-z0-9_]+)"),
                    (r'/begin\s+MEASUREMENT\s+([A-Za-z0-9_]+)\s*', "With optional trailing space"),
                    (r'/begin\s+MEASUREMENT\s+([^\s]+)', "Non-whitespace pattern"),
                    (r'/begin\s+MEASUREMENT\s+(.+?)(?:\s|$)', "Capture until space or end"),
                ]
                
                for pattern, description in patterns:
                    match = re.match(pattern, line_content.strip(), re.IGNORECASE)
                    if match:
                        captured = match.group(1)
                        print(f"    ✅ {description}: '{captured}' (len={len(captured)})")
                        
                        # Check if captured signal matches our target
                        if captured.lower() == target_signal.lower():
                            print(f"      🎯 EXACT MATCH with target signal!")
                        elif target_signal.lower() in captured.lower():
                            print(f"      🔍 Contains target signal")
                    else:
                        print(f"    ❌ {description}: No match")
        
        else:
            print("❌ No lines found containing the signal")
            
            # Try searching for parts
            print(f"\n🔍 Searching for signal parts:")
            parts = ["LDP", "TCO", "IGA", "ST"]
            
            for part in parts:
                part_lines = []
                for i, line in enumerate(lines, 1):
                    if part.lower() in line.lower() and ('begin' in line.lower()):
                        part_lines.append((i, line.strip()))
                
                if part_lines:
                    print(f"\n  Found '{part}' in {len(part_lines)} line(s):")
                    for line_num, line_content in part_lines[:3]:  # Show first 3
                        print(f"    Line {line_num}: {line_content}")
        
        # Also search for CHARACTERISTIC blocks
        print(f"\n🔍 Searching for CHARACTERISTIC blocks with signal:")
        
        char_matching_lines = []
        for i, line in enumerate(lines, 1):
            if (target_signal.lower() in line.lower() and 
                'characteristic' in line.lower()):
                char_matching_lines.append((i, line))
        
        if char_matching_lines:
            print(f"✅ Found {len(char_matching_lines)} CHARACTERISTIC line(s):")
            for line_num, line_content in char_matching_lines:
                print(f"  Line {line_num}: '{line_content.strip()}'")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def create_flexible_search_function():
    """Create a more flexible search function that handles whitespace issues."""
    print(f"\n" + "=" * 40)
    print("CREATING FLEXIBLE SEARCH FUNCTION")
    print("=" * 40)
    
    flexible_search_code = '''
def search_signal_flexible(signal_name: str, file_path: str):
    """Flexible search that handles whitespace and formatting issues."""
    signal_name_lower = signal_name.lower()
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            current_section = None
            current_signal = None
            signal_content = []
            in_target_signal = False
            
            for line in file:
                line_stripped = line.strip()
                
                # More flexible regex patterns that handle extra whitespace
                measurement_match = re.match(r'/begin\\s+MEASUREMENT\\s+([^\\s]+)', line_stripped, re.IGNORECASE)
                characteristic_match = re.match(r'/begin\\s+CHARACTERISTIC\\s+([^\\s]+)', line_stripped, re.IGNORECASE)
                
                if measurement_match:
                    current_section = 'MEASUREMENT'
                    current_signal = measurement_match.group(1).strip()
                    signal_content = []
                    in_target_signal = (current_signal.lower() == signal_name_lower)
                    continue
                
                elif characteristic_match:
                    current_section = 'CHARACTERISTIC'
                    current_signal = characteristic_match.group(1).strip()
                    signal_content = []
                    in_target_signal = (current_signal.lower() == signal_name_lower)
                    continue
                
                # Check for end of section
                if re.match(r'/end\\s+(MEASUREMENT|CHARACTERISTIC)', line_stripped, re.IGNORECASE):
                    if in_target_signal and current_signal:
                        # Found our target signal
                        content_str = '\\n'.join(signal_content)
                        return {
                            'name': current_signal,
                            'type': current_section,
                            'description': extract_description(content_str),
                            'raw_content': content_str
                        }
                    
                    current_section = None
                    current_signal = None
                    signal_content = []
                    in_target_signal = False
                    continue
                
                # Collect content if we're in the target signal
                if in_target_signal:
                    signal_content.append(line_stripped)
        
        return None
        
    except Exception as e:
        print(f"Error in flexible search: {e}")
        return None

def extract_description(content: str) -> str:
    """Extract description from signal content."""
    desc_match = re.search(r'"([^"]*)"', content)
    return desc_match.group(1) if desc_match else 'No description found'
'''
    
    print("Flexible search function created!")
    print("This function uses pattern: r'/begin\\s+MEASUREMENT\\s+([^\\s]+)'")
    print("Which captures everything until the first whitespace character.")
    
    return flexible_search_code

if __name__ == "__main__":
    debug_whitespace_issues()
    create_flexible_search_function()
