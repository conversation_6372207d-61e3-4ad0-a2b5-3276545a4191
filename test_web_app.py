#!/usr/bin/env python3
"""
Test script for the A2L Signal Search Web Application

This script tests the web application functionality without starting the server.
"""

import os
import tempfile
import sys

# Sample A2L content for testing
SAMPLE_A2L_CONTENT = '''
/begin MEASUREMENT eng_hour_ofs
   "LIF NVRAM, ECU replacement offset for ENG HOUR"
   UWORD
   _CNV_A_R_LINEAR_____0_CM
   1
   100.
   0.
   6553.5
   DISPLAY_IDENTIFIER ENG_HOUR_OFS
   ECU_ADDRESS 0x40005c70
/end MEASUREMENT

/begin CHARACTERISTIC c_abc_inc_conv_mon
   "Anti bounce counter increment"
   VALUE
   0x80005248
   _REC_S1VAL_20_U1
   255.
   _CNV_A_R_LINEAR_____3_CM
   0.
   255.
   DISPLAY_IDENTIFIER C_ABC_INC_CONV_MON
/end CHARACTERISTIC
'''

def test_search_functionality():
    """Test the search functionality."""
    print("Testing A2L Signal Search Web Application")
    print("=" * 50)
    
    # Create a temporary A2L file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.a2l', delete=False, encoding='utf-8') as f:
        f.write(SAMPLE_A2L_CONTENT)
        test_file = f.name
    
    try:
        # Import the search function from app.py
        from app import search_signal_in_a2l_web
        
        # Test cases
        test_cases = [
            ('eng_hour_ofs', True, 'MEASUREMENT'),
            ('ENG_HOUR_OFS', True, 'MEASUREMENT'),  # Case insensitive
            ('c_abc_inc_conv_mon', True, 'CHARACTERISTIC'),
            ('C_ABC_INC_CONV_MON', True, 'CHARACTERISTIC'),  # Case insensitive
            ('nonexistent_signal', False, None)
        ]
        
        print("Running test cases...")
        print("-" * 30)
        
        passed = 0
        total = len(test_cases)
        
        for signal_name, should_find, expected_type in test_cases:
            print(f"\nTesting: '{signal_name}'")
            
            result = search_signal_in_a2l_web(signal_name, test_file)
            
            if should_find:
                if result and result['type'] == expected_type:
                    print(f"✓ PASS - Found {result['type']}: {result['name']}")
                    print(f"  Description: {result['description']}")
                    passed += 1
                else:
                    print(f"✗ FAIL - Expected {expected_type}, got {result}")
            else:
                if result is None:
                    print("✓ PASS - Signal correctly not found")
                    passed += 1
                else:
                    print(f"✗ FAIL - Should not have found signal, but got: {result}")
        
        print("\n" + "=" * 50)
        print("TEST RESULTS")
        print("=" * 50)
        print(f"Passed: {passed}/{total}")
        print(f"Success rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 ALL TESTS PASSED!")
            print("The web application search functionality is working correctly.")
        else:
            print(f"\n⚠️ {total-passed} test(s) failed.")
        
        # Test detailed output
        print("\n" + "=" * 50)
        print("DETAILED OUTPUT TEST")
        print("=" * 50)
        
        result = search_signal_in_a2l_web('eng_hour_ofs', test_file)
        if result:
            print("Signal found with details:")
            for key, value in result.items():
                if value and key != 'raw_content':
                    print(f"  {key}: {value}")
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        print("Make sure app.py is in the current directory and Flask is installed.")
    except Exception as e:
        print(f"✗ Test error: {e}")
    finally:
        # Clean up
        if os.path.exists(test_file):
            os.unlink(test_file)
            print(f"\nCleaned up test file: {test_file}")

def check_files():
    """Check if all required files exist."""
    print("Checking required files...")
    print("-" * 30)
    
    required_files = [
        'app.py',
        'templates/index.html',
        'requirements.txt',
        'run_web_app.py'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} - MISSING")
            all_exist = False
    
    return all_exist

def main():
    """Main test function."""
    print("A2L Signal Search Web Application - Test Suite")
    print("=" * 60)
    
    # Check files
    if not check_files():
        print("\n⚠️ Some required files are missing. Please ensure all files are present.")
        return
    
    print("\n✓ All required files are present.")
    
    # Test functionality
    print("\n" + "=" * 60)
    test_search_functionality()
    
    print("\n" + "=" * 60)
    print("USAGE INSTRUCTIONS")
    print("=" * 60)
    print("To start the web application:")
    print("1. Install Flask: pip install Flask==2.3.3 Werkzeug==2.3.7")
    print("2. Run: python run_web_app.py")
    print("3. Open browser: http://localhost:5000")
    print("4. Upload A2L file and search for signals")

if __name__ == "__main__":
    main()
