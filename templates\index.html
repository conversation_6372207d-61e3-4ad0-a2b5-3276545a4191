<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A2L Signal Search</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .upload-section, .search-section {
            margin-bottom: 30px;
            padding: 25px;
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .upload-section:hover, .search-section:hover {
            border-color: #4facfe;
            background-color: #f8f9ff;
        }

        .section-title {
            font-size: 1.3em;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 20px;
            background: #4facfe;
            margin-right: 10px;
            border-radius: 2px;
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            cursor: pointer;
            width: 100%;
        }

        .file-input {
            display: none;
        }

        .file-input-button {
            display: inline-block;
            padding: 12px 25px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            width: 100%;
            text-align: center;
        }

        .file-input-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .search-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 1em;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .search-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .search-button {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .search-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .status {
            margin: 15px 0;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .result-section {
            margin-top: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #4facfe;
        }

        .signal-name {
            font-size: 1.4em;
            color: #333;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .signal-description {
            font-size: 1.1em;
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .more-info-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .more-info-button:hover {
            background: #218838;
            transform: translateY(-1px);
        }

        .details-section {
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            display: none;
        }

        .details-section.show {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .detail-item {
            margin-bottom: 12px;
            display: flex;
            align-items: flex-start;
        }

        .detail-label {
            font-weight: 600;
            color: #333;
            min-width: 150px;
            margin-right: 15px;
        }

        .detail-value {
            color: #666;
            flex: 1;
            word-break: break-all;
        }

        .loading {
            display: none;
            text-align: center;
            color: #666;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .clear-button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 0.8em;
            margin-left: 10px;
            transition: all 0.3s ease;
        }

        .clear-button:hover {
            background: #c82333;
        }

        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>A2L Signal Search</h1>
            <p>Upload your A2L file and search for signal information</p>
        </div>
        
        <div class="content">
            <!-- File Upload Section -->
            <div class="upload-section">
                <div class="section-title">📁 Upload A2L File</div>
                <div class="file-input-wrapper">
                    <input type="file" id="fileInput" class="file-input" accept=".a2l,.A2L">
                    <label for="fileInput" class="file-input-button">
                        Choose A2L File
                    </label>
                </div>
                <div id="uploadStatus"></div>
            </div>

            <!-- Search Section -->
            <div class="search-section">
                <div class="section-title">🔍 Search Signal</div>
                <input type="text" id="signalInput" class="search-input" placeholder="Enter signal name (case insensitive)" disabled>
                <button id="searchButton" class="search-button" disabled>Search Signal</button>
                <div id="searchStatus"></div>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>Searching...</p>
                </div>
            </div>

            <!-- Results Section -->
            <div id="resultSection" style="display: none;">
                <div class="result-section">
                    <div class="signal-name" id="signalName"></div>
                    <div class="signal-description" id="signalDescription"></div>
                    <button class="more-info-button" id="moreInfoButton">More Info</button>
                    
                    <div class="details-section" id="detailsSection">
                        <div id="signalDetails"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let uploadedFile = null;
        let currentSignalDetails = null;

        // File upload handling
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                uploadFile(file);
            }
        });

        // Search button handling
        document.getElementById('searchButton').addEventListener('click', function() {
            const signalName = document.getElementById('signalInput').value.trim();
            if (signalName) {
                searchSignal(signalName);
            }
        });

        // Enter key handling for search input
        document.getElementById('signalInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const signalName = this.value.trim();
                if (signalName) {
                    searchSignal(signalName);
                }
            }
        });

        // More info button handling
        document.getElementById('moreInfoButton').addEventListener('click', function() {
            const detailsSection = document.getElementById('detailsSection');
            if (detailsSection.classList.contains('show')) {
                detailsSection.classList.remove('show');
                this.textContent = 'More Info';
            } else {
                detailsSection.classList.add('show');
                this.textContent = 'Less Info';
                if (currentSignalDetails) {
                    displaySignalDetails(currentSignalDetails);
                }
            }
        });

        function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);

            showStatus('uploadStatus', 'Uploading file...', 'info');

            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showStatus('uploadStatus', data.message + ' <button class="clear-button" onclick="clearFile()">Clear</button>', 'success');
                    uploadedFile = data.filename;
                    enableSearch();
                } else {
                    showStatus('uploadStatus', data.error, 'error');
                }
            })
            .catch(error => {
                showStatus('uploadStatus', 'Upload failed: ' + error.message, 'error');
            });
        }

        function searchSignal(signalName) {
            if (!uploadedFile) {
                showStatus('searchStatus', 'Please upload an A2L file first', 'error');
                return;
            }

            showLoading(true);
            showStatus('searchStatus', '', '');
            document.getElementById('resultSection').style.display = 'none';

            fetch('/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ signal_name: signalName })
            })
            .then(response => response.json())
            .then(data => {
                showLoading(false);
                
                if (data.found) {
                    displaySignalResult(data.signal);
                } else {
                    showStatus('searchStatus', data.message, 'error');
                }
            })
            .catch(error => {
                showLoading(false);
                showStatus('searchStatus', 'Search failed: ' + error.message, 'error');
            });
        }

        function displaySignalResult(signal) {
            document.getElementById('signalName').textContent = signal.name;
            document.getElementById('signalDescription').textContent = signal.description || 'No description available';
            
            currentSignalDetails = signal.details;
            
            // Reset more info button and details
            document.getElementById('moreInfoButton').textContent = 'More Info';
            document.getElementById('detailsSection').classList.remove('show');
            
            document.getElementById('resultSection').style.display = 'block';
        }

        function displaySignalDetails(details) {
            const detailsContainer = document.getElementById('signalDetails');
            let html = '';

            const fields = [
                { label: 'Signal Name', value: details.name },
                { label: 'Type', value: details.type },
                { label: 'Description', value: details.description },
            ];

            if (details.type === 'MEASUREMENT') {
                if (details.data_type) fields.push({ label: 'Data Type', value: details.data_type });
                if (details.ecu_address) fields.push({ label: 'ECU Address', value: details.ecu_address });
            } else if (details.type === 'CHARACTERISTIC') {
                if (details.characteristic_type) fields.push({ label: 'Characteristic Type', value: details.characteristic_type });
                if (details.address) fields.push({ label: 'Address', value: details.address });
                if (details.bit_mask) fields.push({ label: 'Bit Mask', value: details.bit_mask });
            }

            if (details.display_identifier) fields.push({ label: 'Display Identifier', value: details.display_identifier });
            if (details.conversion) fields.push({ label: 'Conversion Method', value: details.conversion });
            if (details.lower_limit && details.upper_limit) {
                fields.push({ label: 'Range', value: `${details.lower_limit} to ${details.upper_limit}` });
            }

            fields.forEach(field => {
                if (field.value) {
                    html += `
                        <div class="detail-item">
                            <div class="detail-label">${field.label}:</div>
                            <div class="detail-value">${field.value}</div>
                        </div>
                    `;
                }
            });

            detailsContainer.innerHTML = html;
        }

        function clearFile() {
            fetch('/clear', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                uploadedFile = null;
                document.getElementById('fileInput').value = '';
                showStatus('uploadStatus', 'File cleared', 'info');
                disableSearch();
                document.getElementById('resultSection').style.display = 'none';
            });
        }

        function enableSearch() {
            document.getElementById('signalInput').disabled = false;
            document.getElementById('searchButton').disabled = false;
        }

        function disableSearch() {
            document.getElementById('signalInput').disabled = true;
            document.getElementById('searchButton').disabled = true;
            showStatus('searchStatus', '', '');
        }

        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = message ? `status ${type}` : '';
        }

        function showLoading(show) {
            const loading = document.getElementById('loading');
            if (show) {
                loading.classList.add('show');
            } else {
                loading.classList.remove('show');
            }
        }
    </script>
</body>
</html>
