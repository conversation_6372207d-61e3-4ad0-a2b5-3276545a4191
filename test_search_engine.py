#!/usr/bin/env python3
"""
Test script for the new search engine features
"""

import difflib

def test_fuzzy_matching():
    """Test fuzzy matching functionality."""
    print("TESTING FUZZY MATCHING FUNCTIONALITY")
    print("=" * 50)
    
    # Sample signal names (simulating what might be in an A2L file)
    sample_signals = [
        "LDP_TCO_IP_IGA_ST",
        "LDP_TCO_IP_IGA_STATUS",
        "LDP_TCO__IP_IGA_ST",
        "LDP_TCO_IP_IGA_STATE",
        "LDP_CONTROL_STATUS",
        "TCO_IP_STATUS",
        "IGA_STATUS_FLAG",
        "ENG_HOUR_OFS",
        "ABC_CONV_MON",
        "STATE_CLEAR_KWP",
        "LDP_ACTIVE_FLAG",
        "LDP_WARNING_STATUS"
    ]
    
    # Test cases
    test_queries = [
        "LDP_TCO__IP_IGA_ST",  # Exact match should be found
        "ldp_tco_ip_iga_st",   # Case insensitive
        "LDP_TCO_IP_IGA",      # Partial match
        "LDP_STATUS",          # Should find LDP-related signals
        "CONTROL_STATUS",      # Should find similar
        "NONEXISTENT_SIGNAL",  # Should find best matches
        "ENG_HOUR",            # Partial match
        "ABC_CONV"             # Partial match
    ]
    
    print("Sample signals available:")
    for i, signal in enumerate(sample_signals, 1):
        print(f"  {i:2d}. {signal}")
    
    print(f"\nTesting fuzzy matching:")
    print("-" * 30)
    
    for query in test_queries:
        print(f"\nQuery: '{query}'")
        
        # Exact match check
        exact_matches = [s for s in sample_signals if s.lower() == query.lower()]
        if exact_matches:
            print(f"  ✅ Exact match: {exact_matches[0]}")
            continue
        
        # Fuzzy matching
        close_matches = difflib.get_close_matches(query, sample_signals, n=3, cutoff=0.3)
        
        if close_matches:
            print(f"  🔍 Similar matches:")
            for match in close_matches:
                ratio = difflib.SequenceMatcher(None, query.lower(), match.lower()).ratio()
                similarity_percent = int(ratio * 100)
                print(f"     {match} - {similarity_percent}% match")
        else:
            print(f"  ❌ No similar matches found")

def test_search_suggestions():
    """Test search suggestion functionality."""
    print(f"\n" + "=" * 50)
    print("TESTING SEARCH SUGGESTIONS")
    print("=" * 50)
    
    # Sample signal names
    sample_signals = [
        "LDP_TCO_IP_IGA_ST",
        "LDP_TCO_IP_IGA_STATUS", 
        "LDP_CONTROL_STATUS",
        "LDP_ACTIVE_FLAG",
        "LDP_WARNING_STATUS",
        "ENG_HOUR_OFS",
        "ENG_SPEED_KMH",
        "ENG_TEMP_CELSIUS",
        "ABC_CONV_MON",
        "ABC_ERROR_COUNT",
        "STATE_CLEAR_KWP",
        "STATE_INIT_FLAG"
    ]
    
    # Test partial inputs
    test_inputs = [
        "LD",      # Should suggest LDP signals
        "LDP",     # Should suggest all LDP signals
        "ENG",     # Should suggest ENG signals
        "ST",      # Should suggest signals containing ST
        "ABC",     # Should suggest ABC signals
        "STATUS"   # Should suggest signals with STATUS
    ]
    
    print("Available signals:")
    for signal in sorted(sample_signals):
        print(f"  {signal}")
    
    print(f"\nTesting search suggestions:")
    print("-" * 30)
    
    for input_text in test_inputs:
        print(f"\nInput: '{input_text}'")
        
        # Find suggestions (starts with)
        starts_with = [s for s in sample_signals if s.lower().startswith(input_text.lower())]
        
        # Find suggestions (contains)
        contains = [s for s in sample_signals if input_text.lower() in s.lower() and s not in starts_with]
        
        suggestions = starts_with + contains
        suggestions = suggestions[:5]  # Limit to 5
        
        if suggestions:
            print(f"  Suggestions:")
            for suggestion in suggestions:
                print(f"    {suggestion}")
        else:
            print(f"  No suggestions found")

def demonstrate_search_flow():
    """Demonstrate the complete search flow."""
    print(f"\n" + "=" * 50)
    print("SEARCH FLOW DEMONSTRATION")
    print("=" * 50)
    
    print("1. User types 'LDP' -> Shows suggestions starting with LDP")
    print("2. User types 'LDP_TCO' -> Shows more specific suggestions")
    print("3. User types 'LDP_TCO__IP_IGA_ST' -> Exact match found")
    print("4. User types 'LDP_WRONG_NAME' -> No exact match, shows similar signals")
    print("5. User selects from similar signals -> Searches selected signal")
    
    print(f"\nFeatures implemented:")
    print("✅ Real-time search suggestions as user types")
    print("✅ Auto-complete dropdown with signal names and types")
    print("✅ Fuzzy matching for typos and similar names")
    print("✅ Similarity percentage display")
    print("✅ Keyboard navigation (Up/Down arrows)")
    print("✅ Click or Enter to select suggestions")
    print("✅ Automatic signal indexing when A2L file is loaded")

if __name__ == "__main__":
    test_fuzzy_matching()
    test_search_suggestions()
    demonstrate_search_flow()
