#!/usr/bin/env python3
"""
Test script for the A2L Signal Search GUI Application

This script tests the GUI application functionality.
"""

import tkinter as tk
import tempfile
import os

# Sample A2L content for testing
SAMPLE_A2L_CONTENT = '''
/begin MEASUREMENT eng_hour_ofs
   "LIF NVRAM, ECU replacement offset for ENG HOUR"
   UWORD
   _CNV_A_R_LINEAR_____0_CM
   1
   100.
   0.
   6553.5
   DISPLAY_IDENTIFIER ENG_HOUR_OFS
   ECU_ADDRESS 0x40005c70
/end MEASUREMENT

/begin CHARACTERISTIC c_abc_inc_conv_mon
   "Anti bounce counter increment"
   VALUE
   0x80005248
   _REC_S1VAL_20_U1
   255.
   _CNV_A_R_LINEAR_____3_CM
   0.
   255.
   DISPLAY_IDENTIFIER C_ABC_INC_CONV_MON
/end CHARACTERISTIC
'''

def test_gui_functionality():
    """Test the GUI functionality without actually showing the GUI."""
    print("Testing A2L Signal Search GUI Application")
    print("=" * 50)
    
    try:
        # Import the GUI class
        from a2l_gui import A2LSignalSearchGUI
        
        # Create a temporary A2L file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.a2l', delete=False, encoding='utf-8') as f:
            f.write(SAMPLE_A2L_CONTENT)
            test_file = f.name
        
        print(f"Created test file: {test_file}")
        
        # Create a root window (but don't show it)
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # Create the GUI application
        app = A2LSignalSearchGUI(root)
        
        # Test search functionality
        test_cases = [
            ('eng_hour_ofs', True, 'MEASUREMENT'),
            ('ENG_HOUR_OFS', True, 'MEASUREMENT'),  # Case insensitive
            ('c_abc_inc_conv_mon', True, 'CHARACTERISTIC'),
            ('nonexistent_signal', False, None)
        ]
        
        print("\nTesting search functionality...")
        print("-" * 30)
        
        passed = 0
        total = len(test_cases)
        
        for signal_name, should_find, expected_type in test_cases:
            print(f"\nTesting: '{signal_name}'")
            
            try:
                result = app.search_signal_in_a2l(signal_name, test_file)
                
                if should_find:
                    if result and result['type'] == expected_type:
                        print(f"✓ PASS - Found {result['type']}: {result['name']}")
                        print(f"  Description: {result['description']}")
                        passed += 1
                    else:
                        print(f"✗ FAIL - Expected {expected_type}, got {result}")
                else:
                    if result is None:
                        print("✓ PASS - Signal correctly not found")
                        passed += 1
                    else:
                        print(f"✗ FAIL - Should not have found signal, but got: {result}")
            
            except Exception as e:
                print(f"✗ ERROR - {e}")
        
        print("\n" + "=" * 50)
        print("TEST RESULTS")
        print("=" * 50)
        print(f"Passed: {passed}/{total}")
        print(f"Success rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 ALL TESTS PASSED!")
            print("The GUI application is working correctly.")
        else:
            print(f"\n⚠️ {total-passed} test(s) failed.")
        
        # Clean up
        root.destroy()
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        print("Make sure a2l_gui.py is in the current directory.")
    except Exception as e:
        print(f"✗ Test error: {e}")
    finally:
        # Clean up test file
        if 'test_file' in locals() and os.path.exists(test_file):
            os.unlink(test_file)
            print(f"\nCleaned up test file: {test_file}")

def check_gui_requirements():
    """Check if GUI requirements are met."""
    print("Checking GUI requirements...")
    print("-" * 30)
    
    try:
        import tkinter
        print("✓ tkinter is available")
        
        # Test basic tkinter functionality
        root = tk.Tk()
        root.withdraw()
        root.destroy()
        print("✓ tkinter GUI functionality works")
        
        return True
        
    except ImportError:
        print("✗ tkinter is not available")
        print("  tkinter should be included with Python by default")
        return False
    except Exception as e:
        print(f"✗ tkinter error: {e}")
        return False

def main():
    """Main test function."""
    print("A2L Signal Search GUI - Test Suite")
    print("=" * 40)
    
    # Check requirements
    if not check_gui_requirements():
        print("\n⚠️ GUI requirements not met.")
        return
    
    # Check if GUI file exists
    if not os.path.exists('a2l_gui.py'):
        print("✗ a2l_gui.py not found")
        return
    
    print("✓ a2l_gui.py found")
    
    # Test functionality
    print("\n" + "=" * 40)
    test_gui_functionality()
    
    print("\n" + "=" * 40)
    print("USAGE INSTRUCTIONS")
    print("=" * 40)
    print("To run the GUI application:")
    print("1. Double-click 'run_gui.bat' (Windows)")
    print("2. Or run: python a2l_gui.py")
    print("3. Use the Browse button to select your A2L file")
    print("4. Enter signal name and click Search")
    print("5. View description, click 'More Info' for details")

if __name__ == "__main__":
    main()
