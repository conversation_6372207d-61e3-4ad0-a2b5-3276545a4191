#!/usr/bin/env python3
"""
Quick test to check if the signal search is working
"""

import os

def quick_test_signal(signal_name, a2l_file_path):
    """Quick test of signal search functionality."""
    print(f"Quick Test: Searching for '{signal_name}' in '{a2l_file_path}'")
    print("=" * 60)
    
    if not os.path.exists(a2l_file_path):
        print(f"❌ A2L file not found: {a2l_file_path}")
        return
    
    try:
        # Import the search function from main.py
        from main import search_signal_in_a2l
        
        print("🔍 Searching...")
        result = search_signal_in_a2l(signal_name, a2l_file_path)
        
        if result:
            print("✅ SIGNAL FOUND!")
            print(f"   Name: {result['name']}")
            print(f"   Type: {result['type']}")
            print(f"   Description: {result['description']}")
            if result.get('data_type'):
                print(f"   Data Type: {result['data_type']}")
            if result.get('ecu_address'):
                print(f"   ECU Address: {result['ecu_address']}")
        else:
            print("❌ SIGNAL NOT FOUND")
            print("\nTrying case variations...")
            
            # Try different case variations
            variations = [
                signal_name.upper(),
                signal_name.lower(),
                signal_name.title()
            ]
            
            for variation in variations:
                if variation != signal_name:
                    print(f"   Trying: {variation}")
                    result = search_signal_in_a2l(variation, a2l_file_path)
                    if result:
                        print(f"   ✅ Found with variation: {result['name']}")
                        return
            
            print("   ❌ No variations found either")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure main.py is in the current directory")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main function."""
    signal_name = input("Enter signal name to test: ").strip()
    if not signal_name:
        signal_name = "LDP_TCO__IP_IGA_ST"
    
    a2l_file = input("Enter A2L file path (or press Enter for default): ").strip()
    if not a2l_file:
        a2l_file = "FS_RNCP3_3b_B40_4veh.a2l"
    
    quick_test_signal(signal_name, a2l_file)

if __name__ == "__main__":
    main()
