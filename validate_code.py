#!/usr/bin/env python3
"""
Validate the main.py code for syntax and basic functionality
"""

import ast
import sys

def validate_python_syntax(filename):
    """Validate Python syntax of a file."""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # Parse the AST to check for syntax errors
        ast.parse(source)
        print(f"✓ {filename}: Syntax is valid")
        return True
        
    except SyntaxError as e:
        print(f"✗ {filename}: Syntax error at line {e.lineno}: {e.msg}")
        return False
    except Exception as e:
        print(f"✗ {filename}: Error reading file: {e}")
        return False

def validate_imports(filename):
    """Check if all imports are available."""
    try:
        import sys
        import re
        import os
        from typing import Dict, Optional
        print(f"✓ {filename}: All required imports are available")
        return True
    except ImportError as e:
        print(f"✗ {filename}: Import error: {e}")
        return False

def main():
    """Main validation function."""
    print("Validating A2L Search Application")
    print("=" * 40)
    
    files_to_validate = [
        'main.py',
        'a2l_stream_search.py',
        'a2l_search.py'
    ]
    
    all_valid = True
    
    for filename in files_to_validate:
        print(f"\nValidating {filename}...")
        
        # Check if file exists
        try:
            with open(filename, 'r') as f:
                pass
            print(f"✓ {filename}: File exists")
        except FileNotFoundError:
            print(f"✗ {filename}: File not found")
            all_valid = False
            continue
        
        # Validate syntax
        if not validate_python_syntax(filename):
            all_valid = False
            continue
        
        # Validate imports
        if not validate_imports(filename):
            all_valid = False
            continue
    
    print("\n" + "=" * 40)
    if all_valid:
        print("✓ All validations passed!")
        print("\nThe A2L search application is ready to use.")
        print("\nUsage examples:")
        print("  python main.py signal_name")
        print("  python main.py signal_name file.a2l")
        print("  python main.py  # for interactive mode")
    else:
        print("✗ Some validations failed!")
    
    return all_valid

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
