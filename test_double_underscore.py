#!/usr/bin/env python3
"""
Test script to verify the fix for signal names with double underscores

This script tests the updated regex patterns to ensure they can handle
signal names like LDP_TCO__IP_IGA_ST with double underscores.
"""

import re
import tempfile
import os

# Sample A2L content with double underscore signal names
SAMPLE_A2L_WITH_DOUBLE_UNDERSCORE = '''
/begin MEASUREMENT LDP_TCO__IP_IGA_ST
   "Lane Departure Prevention - Traffic Control Object IP IGA Status"
   UBYTE
   _CNV_A_R_LINEAR_____3_CM
   1
   100.
   0.
   255.
   DISPLAY_IDENTIFIER LDP_TCO__IP_IGA_ST
   ECU_ADDRESS 0x40005abc
/end MEASUREMENT

/begin MEASUREMENT normal_signal_name
   "Normal signal without double underscores"
   UWORD
   _CNV_A_R_LINEAR_____0_CM
   1
   100.
   0.
   65535.
   DISPLAY_IDENTIFIER NORMAL_SIGNAL_NAME
   ECU_ADDRESS 0x40005def
/end MEASUREMENT

/begin CHARACTERISTIC C_LDP__MAX__SPEED_KMH
   "LDP Maximum Speed in KMH with double underscores"
   VALUE
   0x80005678
   _REC_S1VAL_20_U1
   255.
   _CNV_A_R_LINEAR_____3_CM
   0.
   255.
   DISPLAY_IDENTIFIER C_LDP__MAX__SPEED_KMH
/end CHARACTERISTIC
'''

def test_regex_patterns():
    """Test the updated regex patterns."""
    print("Testing Regex Patterns for Double Underscore Signal Names")
    print("=" * 60)
    
    # Test the old pattern (should fail)
    old_pattern = r'/begin\s+MEASUREMENT\s+(\w+)'
    new_pattern = r'/begin\s+MEASUREMENT\s+([A-Za-z0-9_]+)'
    
    test_lines = [
        '/begin MEASUREMENT LDP_TCO__IP_IGA_ST',
        '/begin MEASUREMENT normal_signal_name',
        '/begin CHARACTERISTIC C_LDP__MAX__SPEED_KMH'
    ]
    
    print("Testing regex patterns:")
    print("-" * 30)
    
    for line in test_lines:
        print(f"\nTesting line: {line}")
        
        # Test old pattern
        old_match = re.match(old_pattern, line, re.IGNORECASE)
        if old_match:
            print(f"  Old pattern (\\w+): ✓ Found '{old_match.group(1)}'")
        else:
            print(f"  Old pattern (\\w+): ✗ No match")
        
        # Test new pattern
        new_match = re.match(new_pattern, line, re.IGNORECASE)
        if new_match:
            print(f"  New pattern ([A-Za-z0-9_]+): ✓ Found '{new_match.group(1)}'")
        else:
            print(f"  New pattern ([A-Za-z0-9_]+): ✗ No match")

def test_search_functionality():
    """Test the actual search functionality with double underscore signals."""
    print("\n" + "=" * 60)
    print("Testing Search Functionality")
    print("=" * 60)
    
    # Create temporary A2L file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.a2l', delete=False, encoding='utf-8') as f:
        f.write(SAMPLE_A2L_WITH_DOUBLE_UNDERSCORE)
        test_file = f.name
    
    try:
        # Import the search function from main.py
        from main import search_signal_in_a2l
        
        # Test cases with double underscore signals
        test_cases = [
            ('LDP_TCO__IP_IGA_ST', True, 'MEASUREMENT'),
            ('ldp_tco__ip_iga_st', True, 'MEASUREMENT'),  # Case insensitive
            ('LDP_TCO__IP_IGA_ST', True, 'MEASUREMENT'),  # Exact case
            ('normal_signal_name', True, 'MEASUREMENT'),
            ('C_LDP__MAX__SPEED_KMH', True, 'CHARACTERISTIC'),
            ('c_ldp__max__speed_kmh', True, 'CHARACTERISTIC'),  # Case insensitive
            ('nonexistent__signal', False, None)
        ]
        
        print("Testing search functionality:")
        print("-" * 30)
        
        passed = 0
        total = len(test_cases)
        
        for signal_name, should_find, expected_type in test_cases:
            print(f"\nSearching for: '{signal_name}'")
            
            try:
                result = search_signal_in_a2l(signal_name, test_file)
                
                if should_find:
                    if result and result['type'] == expected_type:
                        print(f"  ✓ PASS - Found {result['type']}: {result['name']}")
                        print(f"    Description: {result['description']}")
                        passed += 1
                    else:
                        print(f"  ✗ FAIL - Expected {expected_type}, got {result}")
                else:
                    if result is None:
                        print("  ✓ PASS - Signal correctly not found")
                        passed += 1
                    else:
                        print(f"  ✗ FAIL - Should not have found signal, but got: {result}")
            
            except Exception as e:
                print(f"  ✗ ERROR - {e}")
        
        print("\n" + "-" * 30)
        print(f"Test Results: {passed}/{total} passed ({(passed/total)*100:.1f}%)")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! Double underscore signals work correctly.")
        else:
            print(f"⚠️ {total-passed} test(s) failed.")
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Make sure main.py is in the current directory.")
    except Exception as e:
        print(f"Test error: {e}")
    finally:
        # Clean up
        if os.path.exists(test_file):
            os.unlink(test_file)
            print(f"\nCleaned up test file: {test_file}")

def test_gui_functionality():
    """Test the GUI search functionality with double underscore signals."""
    print("\n" + "=" * 60)
    print("Testing GUI Search Functionality")
    print("=" * 60)
    
    # Create temporary A2L file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.a2l', delete=False, encoding='utf-8') as f:
        f.write(SAMPLE_A2L_WITH_DOUBLE_UNDERSCORE)
        test_file = f.name
    
    try:
        # Import the GUI class
        import tkinter as tk
        from a2l_gui import A2LSignalSearchGUI
        
        # Create a hidden root window
        root = tk.Tk()
        root.withdraw()
        
        # Create GUI instance
        app = A2LSignalSearchGUI(root)
        
        # Test the search method directly
        test_signal = 'LDP_TCO__IP_IGA_ST'
        print(f"Testing GUI search for: '{test_signal}'")
        
        result = app.search_signal_in_a2l(test_signal, test_file)
        
        if result:
            print(f"✓ GUI SEARCH SUCCESS!")
            print(f"  Signal: {result['name']}")
            print(f"  Type: {result['type']}")
            print(f"  Description: {result['description']}")
        else:
            print("✗ GUI SEARCH FAILED - Signal not found")
        
        # Clean up
        root.destroy()
        
    except ImportError as e:
        print(f"GUI test skipped - Import error: {e}")
    except Exception as e:
        print(f"GUI test error: {e}")
    finally:
        # Clean up
        if os.path.exists(test_file):
            os.unlink(test_file)

def main():
    """Main test function."""
    print("Double Underscore Signal Name Fix - Test Suite")
    print("=" * 60)
    print("Testing fix for signal names like: LDP_TCO__IP_IGA_ST")
    print("=" * 60)
    
    # Test regex patterns
    test_regex_patterns()
    
    # Test search functionality
    test_search_functionality()
    
    # Test GUI functionality
    test_gui_functionality()
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    print("✅ Updated regex pattern from (\\w+) to ([A-Za-z0-9_]+)")
    print("✅ Fixed main.py search function")
    print("✅ Fixed a2l_gui.py search function")
    print("✅ Fixed app.py web application")
    print("✅ Now supports signal names with double underscores")
    print("\nYour signal 'LDP_TCO__IP_IGA_ST' should now be found correctly!")

if __name__ == "__main__":
    main()
