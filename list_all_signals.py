#!/usr/bin/env python3
"""
List all signals in an A2L file

This script will show you all the signals present in your A2L file
so you can verify the exact name of the signal you're looking for.
"""

import re
import os

def list_all_signals(a2l_file_path, search_filter=None):
    """List all signals in the A2L file."""
    print(f"Listing all signals in: {a2l_file_path}")
    print("=" * 80)
    
    if not os.path.exists(a2l_file_path):
        print(f"❌ ERROR: A2L file '{a2l_file_path}' not found!")
        return
    
    try:
        with open(a2l_file_path, 'r', encoding='utf-8', errors='ignore') as file:
            content = file.read()
        
        print(f"✅ File loaded successfully ({len(content)} characters)")
        print()
        
        # Find all MEASUREMENT and CHARACTERISTIC signals
        measurement_pattern = r'/begin\s+MEASUREMENT\s+([A-Za-z0-9_]+)'
        characteristic_pattern = r'/begin\s+CHARACTERISTIC\s+([A-Za-z0-9_]+)'
        
        measurement_signals = []
        characteristic_signals = []
        
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            measurement_match = re.match(measurement_pattern, line.strip(), re.IGNORECASE)
            if measurement_match:
                measurement_signals.append((i, measurement_match.group(1)))
            
            characteristic_match = re.match(characteristic_pattern, line.strip(), re.IGNORECASE)
            if characteristic_match:
                characteristic_signals.append((i, characteristic_match.group(1)))
        
        # Apply search filter if provided
        if search_filter:
            search_filter = search_filter.lower()
            measurement_signals = [(line, sig) for line, sig in measurement_signals 
                                 if search_filter in sig.lower()]
            characteristic_signals = [(line, sig) for line, sig in characteristic_signals 
                                     if search_filter in sig.lower()]
        
        print(f"MEASUREMENT SIGNALS: {len(measurement_signals)}")
        print("-" * 40)
        if measurement_signals:
            for i, (line_num, signal_name) in enumerate(measurement_signals, 1):
                print(f"{i:4d}. {signal_name} (Line {line_num})")
                if i >= 50:  # Limit to first 50 to avoid overwhelming output
                    print(f"     ... and {len(measurement_signals) - 50} more")
                    break
        else:
            print("No MEASUREMENT signals found" + (f" matching '{search_filter}'" if search_filter else ""))
        
        print()
        print(f"CHARACTERISTIC SIGNALS: {len(characteristic_signals)}")
        print("-" * 40)
        if characteristic_signals:
            for i, (line_num, signal_name) in enumerate(characteristic_signals, 1):
                print(f"{i:4d}. {signal_name} (Line {line_num})")
                if i >= 50:  # Limit to first 50 to avoid overwhelming output
                    print(f"     ... and {len(characteristic_signals) - 50} more")
                    break
        else:
            print("No CHARACTERISTIC signals found" + (f" matching '{search_filter}'" if search_filter else ""))
        
        print()
        print("=" * 80)
        print("SUMMARY")
        print("=" * 80)
        print(f"Total MEASUREMENT signals: {len(measurement_signals)}")
        print(f"Total CHARACTERISTIC signals: {len(characteristic_signals)}")
        print(f"Total signals: {len(measurement_signals) + len(characteristic_signals)}")
        
        # Look for signals containing LDP, TCO, IGA, or ST
        print()
        print("SIGNALS CONTAINING 'LDP', 'TCO', 'IGA', or 'ST':")
        print("-" * 50)
        
        related_signals = []
        search_terms = ['LDP', 'TCO', 'IGA', 'ST']
        
        all_signals = [(line, sig, 'MEASUREMENT') for line, sig in measurement_signals] + \
                     [(line, sig, 'CHARACTERISTIC') for line, sig in characteristic_signals]
        
        for line_num, signal_name, signal_type in all_signals:
            signal_upper = signal_name.upper()
            if any(term in signal_upper for term in search_terms):
                related_signals.append((line_num, signal_name, signal_type))
        
        if related_signals:
            for line_num, signal_name, signal_type in related_signals:
                print(f"  {signal_name} ({signal_type}) - Line {line_num}")
        else:
            print("  No signals found containing LDP, TCO, IGA, or ST")
        
        # Look specifically for signals with double underscores
        print()
        print("SIGNALS WITH DOUBLE UNDERSCORES (__):")
        print("-" * 40)
        
        double_underscore_signals = []
        for line_num, signal_name, signal_type in all_signals:
            if '__' in signal_name:
                double_underscore_signals.append((line_num, signal_name, signal_type))
        
        if double_underscore_signals:
            for line_num, signal_name, signal_type in double_underscore_signals:
                print(f"  {signal_name} ({signal_type}) - Line {line_num}")
        else:
            print("  No signals found with double underscores")
            
    except Exception as e:
        print(f"❌ ERROR reading file: {e}")

def main():
    """Main function to run the signal listing."""
    print("A2L Signal Listing Tool")
    print("=" * 30)
    
    # Default file
    default_file = "FS_RNCP3_3b_B40_4veh.a2l"
    
    # Get input from user
    a2l_file = input(f"Enter A2L file path (default: {default_file}): ").strip()
    if not a2l_file:
        a2l_file = default_file
    
    search_filter = input("Enter search filter (optional, press Enter for all signals): ").strip()
    if not search_filter:
        search_filter = None
    
    print()
    list_all_signals(a2l_file, search_filter)

if __name__ == "__main__":
    main()
