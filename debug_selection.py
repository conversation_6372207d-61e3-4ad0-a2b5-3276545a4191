#!/usr/bin/env python3
"""
Debug script for suggestion selection issues
"""

import tkinter as tk
from tkinter import ttk

def create_test_listbox():
    """Create a test listbox to debug selection issues."""
    print("DEBUGGING SUGGESTION SELECTION")
    print("=" * 40)
    
    root = tk.Tk()
    root.title("Selection Debug Test")
    root.geometry("400x300")
    
    # Create a frame
    frame = ttk.Frame(root, padding="10")
    frame.pack(fill=tk.BOTH, expand=True)
    
    # Create a label
    label = ttk.Label(frame, text="Test Suggestion Selection:")
    label.pack(pady=(0, 10))
    
    # Create a listbox similar to the suggestions listbox
    listbox = tk.Listbox(frame, height=8, font=('Arial', 9), 
                        selectmode=tk.SINGLE, activestyle='dotbox')
    listbox.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
    
    # Add test items
    test_items = [
        "LDPM_TCO (AXIS_PTS)",
        "LDP_TCO__IP_IGA_ST (MEASUREMENT)",
        "LDP_CONTROL_STATUS (CHARACTERISTIC-VAL)",
        "ENGINE_SPEED_KMH (MEASUREMENT)",
        "ENGINE_TORQUE_MAP (CHARACTERISTIC-MAP)"
    ]
    
    for item in test_items:
        listbox.insert(tk.END, item)
    
    # Result label
    result_label = ttk.Label(frame, text="Click an item to test selection", foreground='blue')
    result_label.pack()
    
    # Selection handlers
    def on_click(event):
        index = listbox.nearest(event.y)
        if index >= 0 and index < listbox.size():
            listbox.selection_clear(0, tk.END)
            listbox.selection_set(index)
            listbox.activate(index)
            
            selected_text = listbox.get(index)
            signal_name = selected_text.split(' (')[0]
            result_label.config(text=f"Selected: {signal_name}", foreground='green')
            print(f"Selected: {signal_name}")
    
    def on_double_click(event):
        selection = listbox.curselection()
        if selection:
            selected_text = listbox.get(selection[0])
            signal_name = selected_text.split(' (')[0]
            result_label.config(text=f"Double-clicked: {signal_name}", foreground='red')
            print(f"Double-clicked: {signal_name}")
    
    def on_return(event):
        selection = listbox.curselection()
        if selection:
            selected_text = listbox.get(selection[0])
            signal_name = selected_text.split(' (')[0]
            result_label.config(text=f"Enter pressed: {signal_name}", foreground='purple')
            print(f"Enter pressed: {signal_name}")
    
    # Bind events
    listbox.bind('<Button-1>', on_click)
    listbox.bind('<Double-Button-1>', on_double_click)
    listbox.bind('<Return>', on_return)
    
    # Pre-select first item
    listbox.selection_set(0)
    listbox.activate(0)
    
    print("Test window created. Try:")
    print("1. Single click on items")
    print("2. Double click on items")
    print("3. Use arrow keys and press Enter")
    print("4. Check console output")
    
    root.mainloop()

def test_event_bindings():
    """Test different event binding approaches."""
    print("\nTESTING EVENT BINDING APPROACHES")
    print("=" * 40)
    
    approaches = [
        {
            'name': 'Single Click with Delay',
            'description': 'Click selects, auto-search after delay',
            'events': ['<Button-1>'],
            'pros': ['Allows double-click', 'Visual feedback'],
            'cons': ['Delay might be confusing']
        },
        {
            'name': 'Immediate Click',
            'description': 'Click immediately searches',
            'events': ['<Button-1>'],
            'pros': ['Instant response', 'Clear behavior'],
            'cons': ['No double-click support']
        },
        {
            'name': 'Double Click Only',
            'description': 'Only double-click searches',
            'events': ['<Double-Button-1>'],
            'pros': ['Clear intent', 'No accidental searches'],
            'cons': ['Less intuitive', 'Slower workflow']
        },
        {
            'name': 'Hover + Click',
            'description': 'Hover selects, click searches',
            'events': ['<Motion>', '<Button-1>'],
            'pros': ['Visual feedback', 'Clear selection'],
            'cons': ['More complex', 'Hover might be distracting']
        }
    ]
    
    for approach in approaches:
        print(f"\n{approach['name']}:")
        print(f"  Description: {approach['description']}")
        print(f"  Events: {', '.join(approach['events'])}")
        print(f"  Pros: {', '.join(approach['pros'])}")
        print(f"  Cons: {', '.join(approach['cons'])}")

def show_debugging_tips():
    """Show debugging tips for selection issues."""
    print(f"\n" + "=" * 40)
    print("DEBUGGING TIPS")
    print("=" * 40)
    
    tips = [
        {
            'issue': 'Clicks not registering',
            'solutions': [
                'Check event binding syntax',
                'Verify listbox is properly created',
                'Test with simple print statements',
                'Check for event conflicts'
            ]
        },
        {
            'issue': 'Selection not visible',
            'solutions': [
                'Set selectmode=tk.SINGLE',
                'Use activestyle="dotbox"',
                'Call selection_set() and activate()',
                'Check listbox focus'
            ]
        },
        {
            'issue': 'Focus issues',
            'solutions': [
                'Use after_idle() for delayed actions',
                'Check focus_get() before hiding',
                'Bind FocusOut carefully',
                'Test focus behavior'
            ]
        },
        {
            'issue': 'Event conflicts',
            'solutions': [
                'Use return "break" to stop propagation',
                'Check event binding order',
                'Test individual events separately',
                'Use different event types'
            ]
        }
    ]
    
    for tip in tips:
        print(f"\n{tip['issue']}:")
        for solution in tip['solutions']:
            print(f"  • {solution}")

def show_recommended_fix():
    """Show the recommended fix for selection issues."""
    print(f"\n" + "=" * 40)
    print("RECOMMENDED FIX")
    print("=" * 40)
    
    print("Based on common tkinter issues, try this approach:")
    print()
    
    fix_code = '''
# 1. Simplify event bindings
listbox.bind('<Button-1>', self.on_suggestion_click)
listbox.bind('<Double-Button-1>', self.on_suggestion_double_click)
listbox.bind('<Return>', self.on_suggestion_return)

# 2. Simple click handler
def on_suggestion_click(self, event):
    """Handle single click - select item."""
    index = self.suggestions_listbox.nearest(event.y)
    if 0 <= index < self.suggestions_listbox.size():
        self.suggestions_listbox.selection_clear(0, tk.END)
        self.suggestions_listbox.selection_set(index)
        self.suggestions_listbox.activate(index)
        # Don't search immediately - wait for double-click or Enter

# 3. Double-click handler
def on_suggestion_double_click(self, event):
    """Handle double-click - search immediately."""
    selection = self.suggestions_listbox.curselection()
    if selection:
        self.perform_search_from_selection(selection[0])

# 4. Enter key handler
def on_suggestion_return(self, event):
    """Handle Enter key - search selected item."""
    selection = self.suggestions_listbox.curselection()
    if selection:
        self.perform_search_from_selection(selection[0])
    return 'break'

# 5. Common search function
def perform_search_from_selection(self, index):
    """Perform search from selected suggestion."""
    suggestion_text = self.suggestions_listbox.get(index)
    signal_name = suggestion_text.split(' (')[0]
    self.search_signal_var.set(signal_name)
    self.hide_suggestions()
    self.search_signal()
'''
    
    print(fix_code)

if __name__ == "__main__":
    test_event_bindings()
    show_debugging_tips()
    show_recommended_fix()
    
    # Ask user if they want to run the interactive test
    response = input("\nRun interactive selection test? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        create_test_listbox()
