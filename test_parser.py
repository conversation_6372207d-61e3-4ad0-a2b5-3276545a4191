#!/usr/bin/env python3
"""
Simple test script to verify A2L parsing functionality
"""

import re
import os

def test_a2l_parsing():
    """Test basic A2L parsing functionality."""
    a2l_file = "FS_RNCP3_3b_B40_4veh.a2l"
    
    if not os.path.exists(a2l_file):
        print(f"Error: A2L file '{a2l_file}' not found.")
        return
    
    print("Testing A2L file parsing...")
    
    try:
        with open(a2l_file, 'r', encoding='utf-8', errors='ignore') as file:
            content = file.read()
        
        print(f"File size: {len(content)} characters")
        
        # Test MEASUREMENT pattern
        measurement_pattern = r'/begin\s+MEASUREMENT\s+(\w+)\s*(.*?)/end\s+MEASUREMENT'
        measurements = list(re.finditer(measurement_pattern, content, re.DOTALL | re.IGNORECASE))
        print(f"Found {len(measurements)} MEASUREMENT sections")
        
        # Test CHARACTERISTIC pattern
        characteristic_pattern = r'/begin\s+CHARACTERISTIC\s+(\w+)\s*(.*?)/end\s+CHARACTERISTIC'
        characteristics = list(re.finditer(characteristic_pattern, content, re.DOTALL | re.IGNORECASE))
        print(f"Found {len(characteristics)} CHARACTERISTIC sections")
        
        # Test specific signal search
        test_signals = ['eng_hour_ofs', 'abc_conv_mon', 'state_clear_kwp']
        
        for signal in test_signals:
            # Search in measurements
            found_in_measurements = False
            for match in measurements:
                if match.group(1).lower() == signal.lower():
                    print(f"Found '{signal}' in MEASUREMENT section")
                    signal_content = match.group(2)
                    
                    # Extract description
                    desc_match = re.search(r'"([^"]*)"', signal_content)
                    if desc_match:
                        print(f"  Description: {desc_match.group(1)}")
                    
                    # Extract data type
                    data_type_match = re.search(r'(UBYTE|SBYTE|UWORD|SWORD|ULONG|SLONG|FLOAT32_IEEE)', signal_content)
                    if data_type_match:
                        print(f"  Data Type: {data_type_match.group(1)}")
                    
                    # Extract ECU address
                    ecu_addr_match = re.search(r'ECU_ADDRESS\s+(0x[0-9a-fA-F]+)', signal_content)
                    if ecu_addr_match:
                        print(f"  ECU Address: {ecu_addr_match.group(1)}")
                    
                    found_in_measurements = True
                    break
            
            # Search in characteristics
            found_in_characteristics = False
            for match in characteristics:
                if match.group(1).lower() == signal.lower():
                    print(f"Found '{signal}' in CHARACTERISTIC section")
                    signal_content = match.group(2)
                    
                    # Extract description
                    desc_match = re.search(r'"([^"]*)"', signal_content)
                    if desc_match:
                        print(f"  Description: {desc_match.group(1)}")
                    
                    # Extract characteristic type
                    char_type_match = re.search(r'(VALUE|CURVE|MAP|CUBOID|CUBE_4|CUBE_5)', signal_content)
                    if char_type_match:
                        print(f"  Characteristic Type: {char_type_match.group(1)}")
                    
                    # Extract address
                    addr_match = re.search(r'0x[0-9a-fA-F]+', signal_content)
                    if addr_match:
                        print(f"  Address: {addr_match.group(0)}")
                    
                    found_in_characteristics = True
                    break
            
            if not found_in_measurements and not found_in_characteristics:
                print(f"Signal '{signal}' not found")
            
            print()
        
        print("Test completed successfully!")
        
    except Exception as e:
        print(f"Error during testing: {e}")

if __name__ == "__main__":
    test_a2l_parsing()
