# A2L Signal Search Application

This application searches for signal details in A2L (ASAP2) files. It supports case-insensitive search for signal names and provides comprehensive information about the found signals.

## Features

- **Case-insensitive search**: Search for signals regardless of case
- **Memory-efficient**: Uses streaming approach to handle large A2L files
- **Comprehensive details**: Extracts all relevant signal information
- **Similar signal suggestions**: Finds signals with similar names if exact match not found
- **Multiple signal types**: Supports both MEASUREMENT and CHARACTERISTIC signals

## Files

1. **main.py** - Main application with streaming search (recommended)
2. **a2l_stream_search.py** - Alternative streaming implementation
3. **a2l_search.py** - Alternative implementation for smaller files
4. **test_simple.py** - Basic file existence and size checker
5. **quick_test.py** - Quick test for specific signal search

## Usage

### Command Line Usage

```bash
# Search for a specific signal
python main.py signal_name

# Search for a signal in a specific A2L file
python main.py signal_name path/to/your/file.a2l
```

### Interactive Usage

```bash
# Run without arguments for interactive mode
python main.py
```

The application will prompt you for:
1. Signal name to search
2. A2L file path (defaults to FS_RNCP3_3b_B40_4veh.a2l)

## Examples

### Example 1: Search for a specific signal
```bash
python main.py eng_hour_ofs
```

### Example 2: Search with custom A2L file
```bash
python main.py abc_conv_mon my_custom_file.a2l
```

### Example 3: Interactive mode
```bash
python main.py
Enter signal name to search: state_clear_kwp
Enter A2L file path (default: FS_RNCP3_3b_B40_4veh.a2l): 
```

## Signal Information Extracted

The application extracts the following information for each signal:

### For MEASUREMENT signals:
- Signal name
- Description
- Data type (UBYTE, UWORD, ULONG, etc.)
- ECU Address
- Display Identifier
- Conversion method
- Format
- Value ranges
- Resolution and accuracy

### For CHARACTERISTIC signals:
- Signal name
- Description
- Characteristic type (VALUE, CURVE, MAP, etc.)
- Address
- Display Identifier
- Conversion method
- Format
- Bit mask (if applicable)
- Value ranges

## Sample Output

```
================================================================================
SIGNAL DETAILS: eng_hour_ofs
================================================================================
Type: MEASUREMENT
Description: LIF NVRAM, ECU replacement offset for ENG HOUR
Data Type: UWORD
ECU Address: 0x40005c70
Display Identifier: ENG_HOUR_OFS
Conversion Method: _CNV_A_R_LINEAR_____0_CM
Format: "%6.1"
Range: 0.0 to 6553.5
Resolution: 1
Accuracy: 100.0
================================================================================
```

## Performance Notes

- The application uses a streaming approach to handle very large A2L files efficiently
- Progress indicators show processing status for large files
- Memory usage is optimized to handle files of any size
- Search stops immediately when the target signal is found

## Error Handling

The application handles various error conditions:
- File not found
- Invalid file format
- Encoding issues
- Memory limitations

## Requirements

- Python 3.6 or higher
- No external dependencies (uses only standard library)

## Troubleshooting

### Large File Processing
If your A2L file is very large (>100MB), the application may take some time to process. Progress indicators will show the current status.

### Signal Not Found
If a signal is not found, the application will:
1. Search for similar signals with partial name matches
2. Display up to 10 similar signals
3. Suggest checking the signal name spelling

### Memory Issues
If you encounter memory issues with very large files:
1. Use the streaming version (main.py)
2. Close other applications to free memory
3. Consider splitting large A2L files if possible

## Technical Details

### A2L File Structure
The application parses A2L files looking for:
- `/begin MEASUREMENT` ... `/end MEASUREMENT` blocks
- `/begin CHARACTERISTIC` ... `/end CHARACTERISTIC` blocks

### Search Algorithm
1. Streams through the file line by line
2. Identifies signal blocks using regex patterns
3. Extracts signal information using targeted regex patterns
4. Returns immediately when target signal is found

### Case Sensitivity
All searches are case-insensitive by converting both the search term and signal names to lowercase for comparison.

## License

This application is provided as-is for educational and professional use.
