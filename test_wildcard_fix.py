#!/usr/bin/env python3
"""
Test script to verify the wildcard search fix for *ip*add finding ip_teg_tia_add
"""

def test_wildcard_fix():
    """Test the improved wildcard search algorithm."""
    print("TESTING WILDCARD SEARCH FIX")
    print("=" * 40)
    
    # Test case: *ip*add should find ip_teg_tia_add
    test_cases = [
        {
            'pattern': '*ip*add',
            'target_signal': 'ip_teg_tia_add',
            'should_find': True,
            'description': 'Original problem case'
        },
        {
            'pattern': '*fac*afu',
            'target_signal': 'FAC_AFU_RATIO_ISA',
            'should_find': True,
            'description': 'Previous working case'
        },
        {
            'pattern': '*engine*speed',
            'target_signal': 'ENGINE_SPEED_KMH',
            'should_find': True,
            'description': 'Common pattern'
        },
        {
            'pattern': '*ldp*tco',
            'target_signal': 'LDP_TCO__IP_IGA_ST',
            'should_find': True,
            'description': 'Complex signal name'
        }
    ]
    
    for case in test_cases:
        print(f"\nTest Case: {case['description']}")
        print(f"Pattern: {case['pattern']}")
        print(f"Target: {case['target_signal']}")
        
        # Extract search terms
        search_terms = [term.strip().lower() for term in case['pattern'].replace('*', ' ').split() if term.strip()]
        print(f"Search terms: {search_terms}")
        
        # Calculate score
        score = calculate_improved_wildcard_score(case['target_signal'].lower(), search_terms)
        print(f"Score: {score}")
        
        if case['should_find']:
            if score > 0:
                print("✅ PASS - Signal should be found and has positive score")
            else:
                print("❌ FAIL - Signal should be found but has zero score")
        else:
            if score == 0:
                print("✅ PASS - Signal should not be found and has zero score")
            else:
                print("❌ FAIL - Signal should not be found but has positive score")

def calculate_improved_wildcard_score(signal_lower, search_terms):
    """Improved wildcard scoring algorithm."""
    if not search_terms:
        return 0
    
    score = 0
    signal_parts = signal_lower.replace('_', ' ').split()
    
    # Find positions of each search term in the signal
    term_positions = []
    terms_found = 0
    
    for i, term in enumerate(search_terms):
        best_position = -1
        best_score = 0
        term_found = False
        
        # First check each part of the signal (word boundaries)
        for part_idx, part in enumerate(signal_parts):
            if term in part:
                term_found = True
                part_score = 0
                
                # Score based on match quality
                if term == part:
                    part_score = 25  # Exact match - higher score
                elif part.startswith(term):
                    part_score = 20  # Prefix match
                elif part.endswith(term):
                    part_score = 18  # Suffix match
                else:
                    part_score = 15   # Contains match
                
                # Bonus for early position in signal
                position_bonus = max(0, 8 - part_idx * 1)
                part_score += position_bonus
                
                # Keep track of best match for this term
                if part_score > best_score:
                    best_score = part_score
                    best_position = part_idx
        
        # Also check if term appears anywhere in the full signal (fallback)
        if not term_found and term in signal_lower:
            term_found = True
            best_score = 5  # Higher fallback score
            best_position = signal_lower.find(term) / len(signal_lower)  # Normalized position
        
        if term_found:
            terms_found += 1
            score += best_score
            term_positions.append((i, best_position, best_score))
    
    # Strong bonus for finding all terms - this is crucial for wildcard search
    if terms_found == len(search_terms):
        score += 50  # Increased bonus
    else:
        # Heavy penalty for missing terms in wildcard search
        missing_terms = len(search_terms) - terms_found
        score -= missing_terms * 25
    
    # Check term order bonus (terms should appear in search order)
    if len(term_positions) >= 2:
        order_bonus = 0
        for i in range(len(term_positions) - 1):
            current_pos = term_positions[i][1]
            next_pos = term_positions[i + 1][1]
            
            if current_pos < next_pos:  # Correct order
                order_bonus += 15  # Higher order bonus
            elif current_pos > next_pos:  # Wrong order
                order_bonus -= 3   # Lower penalty for wrong order
        
        score += order_bonus
    
    # Proximity bonus (terms close together score higher)
    if len(term_positions) >= 2:
        positions = [pos[1] for pos in term_positions if isinstance(pos[1], int) and pos[1] >= 0]
        if len(positions) >= 2:
            max_distance = max(positions) - min(positions)
            if max_distance <= 2:  # Terms within 2 words of each other
                score += 20
            elif max_distance <= 4:  # Terms within 4 words
                score += 12
            elif max_distance <= 6:  # Terms within 6 words
                score += 6
    
    # Bonus for signal starting with first search term
    if search_terms and signal_lower.startswith(search_terms[0]):
        score += 30  # Higher starting bonus
    
    # Bonus for signal ending with last search term
    if len(search_terms) > 1 and signal_lower.endswith(search_terms[-1]):
        score += 20
    
    return max(0, score)  # Don't return negative scores

def show_detailed_analysis():
    """Show detailed analysis of the ip_teg_tia_add case."""
    print(f"\n" + "=" * 40)
    print("DETAILED ANALYSIS: *ip*add → ip_teg_tia_add")
    print("=" * 40)
    
    signal = "ip_teg_tia_add"
    pattern = "*ip*add"
    search_terms = ["ip", "add"]
    
    print(f"Signal: {signal}")
    print(f"Pattern: {pattern}")
    print(f"Search terms: {search_terms}")
    print()
    
    signal_parts = signal.replace('_', ' ').split()
    print(f"Signal parts: {signal_parts}")
    print()
    
    print("Term matching:")
    print("- 'ip' matches signal_parts[0] = 'ip' (EXACT MATCH)")
    print("- 'add' matches signal_parts[3] = 'add' (EXACT MATCH)")
    print()
    
    print("Scoring breakdown:")
    print("- 'ip' exact match: 25 points")
    print("- 'ip' position bonus (position 0): 8 points")
    print("- 'add' exact match: 25 points") 
    print("- 'add' position bonus (position 3): 5 points")
    print("- All terms found bonus: 50 points")
    print("- Correct order bonus: 15 points")
    print("- Signal starts with first term: 30 points")
    print("- Signal ends with last term: 20 points")
    print()
    
    total = 25 + 8 + 25 + 5 + 50 + 15 + 30 + 20
    print(f"Total expected score: {total}")
    
    actual_score = calculate_improved_wildcard_score(signal.lower(), search_terms)
    print(f"Actual score: {actual_score}")
    
    if actual_score > 0:
        print("✅ SUCCESS: Signal will be found!")
    else:
        print("❌ PROBLEM: Signal will not be found!")

def show_improvements():
    """Show the improvements made to the algorithm."""
    print(f"\n" + "=" * 40)
    print("ALGORITHM IMPROVEMENTS")
    print("=" * 40)
    
    improvements = [
        {
            'aspect': 'Exact Match Scoring',
            'old': '20 points for exact match',
            'new': '25 points for exact match',
            'benefit': 'Higher priority for exact matches'
        },
        {
            'aspect': 'All Terms Found Bonus',
            'old': '30 points bonus',
            'new': '50 points bonus',
            'benefit': 'Stronger reward for finding all terms'
        },
        {
            'aspect': 'Missing Terms Penalty',
            'old': '15 points penalty per missing term',
            'new': '25 points penalty per missing term',
            'benefit': 'Stronger penalty ensures complete matches rank higher'
        },
        {
            'aspect': 'Order Bonus',
            'old': '10 points for correct order',
            'new': '15 points for correct order',
            'benefit': 'Better reward for maintaining search term order'
        },
        {
            'aspect': 'Starting Bonus',
            'old': '25 points for starting with first term',
            'new': '30 points for starting with first term',
            'benefit': 'Higher priority for signals starting with search term'
        },
        {
            'aspect': 'Ending Bonus',
            'old': 'No ending bonus',
            'new': '20 points for ending with last term',
            'benefit': 'Reward for signals ending with search term'
        },
        {
            'aspect': 'Fallback Scoring',
            'old': '3 points for non-word-boundary matches',
            'new': '5 points for non-word-boundary matches',
            'benefit': 'Better scoring for partial matches'
        }
    ]
    
    for improvement in improvements:
        print(f"\n🔧 {improvement['aspect']}:")
        print(f"   Old: {improvement['old']}")
        print(f"   New: {improvement['new']}")
        print(f"   Benefit: {improvement['benefit']}")

if __name__ == "__main__":
    test_wildcard_fix()
    show_detailed_analysis()
    show_improvements()
