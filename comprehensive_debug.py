#!/usr/bin/env python3
"""
Comprehensive debug script for LDP_TCO__IP_IGA_ST signal
"""

import os
import re

def comprehensive_debug():
    """Comprehensive debugging for the signal search issue."""
    print("COMPREHENSIVE A2L SIGNAL DEBUG")
    print("=" * 50)
    
    a2l_file = "FS_RNCP3_3b_B40_4veh.a2l"
    target_signal = "LDP_TCO__IP_IGA_ST"
    
    # Step 1: Check file existence
    print("1. FILE EXISTENCE CHECK")
    print("-" * 25)
    if os.path.exists(a2l_file):
        file_size = os.path.getsize(a2l_file)
        print(f"✅ File exists: {a2l_file}")
        print(f"   Size: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
    else:
        print(f"❌ File not found: {a2l_file}")
        return
    
    # Step 2: Read file content
    print(f"\n2. READING FILE CONTENT")
    print("-" * 25)
    try:
        with open(a2l_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        print(f"✅ File read successfully")
        print(f"   Characters: {len(content):,}")
        lines = content.split('\n')
        print(f"   Lines: {len(lines):,}")
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return
    
    # Step 3: Simple text search
    print(f"\n3. SIMPLE TEXT SEARCH")
    print("-" * 25)
    print(f"Searching for: '{target_signal}'")
    
    if target_signal in content:
        print("✅ Signal found in file content!")
        
        # Find all occurrences
        occurrences = []
        for i, line in enumerate(lines, 1):
            if target_signal in line:
                occurrences.append((i, line.strip()))
        
        print(f"   Found in {len(occurrences)} line(s):")
        for line_num, line_content in occurrences:
            print(f"   Line {line_num}: {line_content}")
    else:
        print("❌ Signal NOT found in file content")
        
        # Try case variations
        print("\n   Trying case variations:")
        variations = [
            target_signal.upper(),
            target_signal.lower(),
            target_signal.title()
        ]
        
        for variation in variations:
            if variation in content:
                print(f"   ✅ Found variation: {variation}")
                break
        else:
            print("   ❌ No case variations found")
    
    # Step 4: Search for parts of the signal name
    print(f"\n4. PARTIAL SIGNAL SEARCH")
    print("-" * 25)
    parts = ["LDP", "TCO", "IP", "IGA", "ST"]
    
    for part in parts:
        count = content.count(part)
        print(f"   '{part}': {count} occurrences")
        
        if count > 0 and count < 20:  # Show some examples if not too many
            part_lines = []
            for i, line in enumerate(lines, 1):
                if part in line and len(part_lines) < 3:
                    part_lines.append((i, line.strip()))
            
            for line_num, line_content in part_lines:
                print(f"     Line {line_num}: {line_content}")
    
    # Step 5: Search for signals with similar patterns
    print(f"\n5. SIMILAR PATTERN SEARCH")
    print("-" * 25)
    
    # Look for signals with double underscores
    double_underscore_pattern = r'/begin\s+(MEASUREMENT|CHARACTERISTIC)\s+\w*__\w*'
    double_underscore_matches = re.findall(double_underscore_pattern, content, re.IGNORECASE)
    print(f"Signals with double underscores: {len(double_underscore_matches)}")
    
    # Find actual signal names with double underscores
    signal_pattern = r'/begin\s+(MEASUREMENT|CHARACTERISTIC)\s+([A-Za-z0-9_]+)'
    all_signals = re.findall(signal_pattern, content, re.IGNORECASE)
    
    double_underscore_signals = []
    for signal_type, signal_name in all_signals:
        if '__' in signal_name:
            double_underscore_signals.append((signal_type, signal_name))
    
    if double_underscore_signals:
        print(f"Found {len(double_underscore_signals)} signals with double underscores:")
        for signal_type, signal_name in double_underscore_signals[:10]:  # Show first 10
            print(f"   {signal_name} ({signal_type})")
    else:
        print("No signals with double underscores found")
    
    # Step 6: Search for LDP-related signals
    print(f"\n6. LDP-RELATED SIGNALS")
    print("-" * 25)
    
    ldp_signals = []
    for signal_type, signal_name in all_signals:
        if 'LDP' in signal_name.upper():
            ldp_signals.append((signal_type, signal_name))
    
    if ldp_signals:
        print(f"Found {len(ldp_signals)} LDP-related signals:")
        for signal_type, signal_name in ldp_signals:
            print(f"   {signal_name} ({signal_type})")
    else:
        print("No LDP-related signals found")
    
    # Step 7: Test regex patterns
    print(f"\n7. REGEX PATTERN TEST")
    print("-" * 25)
    
    test_lines = [
        f"/begin MEASUREMENT {target_signal}",
        f"/begin CHARACTERISTIC {target_signal}",
        "/begin MEASUREMENT LDP_TCO_IP_IGA_ST",  # Single underscore version
        "/begin MEASUREMENT ldp_tco__ip_iga_st",  # Lowercase version
    ]
    
    patterns = [
        (r'/begin\s+MEASUREMENT\s+(\w+)', "Old pattern (\\w+)"),
        (r'/begin\s+MEASUREMENT\s+([A-Za-z0-9_]+)', "New pattern ([A-Za-z0-9_]+)")
    ]
    
    for test_line in test_lines:
        print(f"\n   Testing: {test_line}")
        for pattern, description in patterns:
            match = re.match(pattern, test_line, re.IGNORECASE)
            if match:
                print(f"     ✅ {description}: '{match.group(1)}'")
            else:
                print(f"     ❌ {description}: No match")
    
    # Step 8: Test the actual search function
    print(f"\n8. SEARCH FUNCTION TEST")
    print("-" * 25)
    
    try:
        from main import search_signal_in_a2l
        
        print(f"Testing search function with: '{target_signal}'")
        result = search_signal_in_a2l(target_signal, a2l_file)
        
        if result:
            print("✅ Search function found the signal!")
            print(f"   Name: {result['name']}")
            print(f"   Type: {result['type']}")
            print(f"   Description: {result['description']}")
        else:
            print("❌ Search function did NOT find the signal")
            
            # Try variations
            print("\n   Trying variations with search function:")
            variations = [target_signal.upper(), target_signal.lower()]
            for variation in variations:
                result = search_signal_in_a2l(variation, a2l_file)
                if result:
                    print(f"   ✅ Found with '{variation}': {result['name']}")
                    break
            else:
                print("   ❌ No variations worked")
        
    except Exception as e:
        print(f"❌ Error testing search function: {e}")
    
    # Step 9: Manual line-by-line search simulation
    print(f"\n9. MANUAL SEARCH SIMULATION")
    print("-" * 25)
    
    print("Simulating the search algorithm...")
    signal_name_lower = target_signal.lower()
    found_in_simulation = False
    
    current_section = None
    current_signal = None
    in_target_signal = False
    
    for i, line in enumerate(lines, 1):
        line_stripped = line.strip()
        
        # Check for MEASUREMENT/CHARACTERISTIC start
        measurement_match = re.match(r'/begin\s+MEASUREMENT\s+([A-Za-z0-9_]+)', line_stripped, re.IGNORECASE)
        characteristic_match = re.match(r'/begin\s+CHARACTERISTIC\s+([A-Za-z0-9_]+)', line_stripped, re.IGNORECASE)
        
        if measurement_match:
            current_section = 'MEASUREMENT'
            current_signal = measurement_match.group(1)
            in_target_signal = (current_signal.lower() == signal_name_lower)
            
            if in_target_signal:
                print(f"   ✅ Found target signal at line {i}: {line_stripped}")
                found_in_simulation = True
                break
        
        elif characteristic_match:
            current_section = 'CHARACTERISTIC'
            current_signal = characteristic_match.group(1)
            in_target_signal = (current_signal.lower() == signal_name_lower)
            
            if in_target_signal:
                print(f"   ✅ Found target signal at line {i}: {line_stripped}")
                found_in_simulation = True
                break
    
    if not found_in_simulation:
        print("   ❌ Manual simulation did not find the signal")
    
    print(f"\n" + "=" * 50)
    print("DEBUG SUMMARY")
    print("=" * 50)
    print(f"Target signal: {target_signal}")
    print(f"File size: {len(content):,} characters")
    print(f"Total signals found: {len(all_signals)}")
    print(f"Signals with double underscores: {len(double_underscore_signals)}")
    print(f"LDP-related signals: {len(ldp_signals)}")
    
    if target_signal in content:
        print("✅ Signal exists in file content")
    else:
        print("❌ Signal does NOT exist in file content")
        print("\n🔍 RECOMMENDATIONS:")
        print("1. Check the exact spelling of the signal name")
        print("2. Verify this is the correct A2L file")
        print("3. The signal might not exist in this file")
        print("4. Try searching for similar signal names")

if __name__ == "__main__":
    comprehensive_debug()
