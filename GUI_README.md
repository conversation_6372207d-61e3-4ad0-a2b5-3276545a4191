# A2L Signal Search - Windows GUI Application

A professional Windows desktop application for searching signals in A2L (ASAP2) files with an intuitive graphical interface.

## 🖥️ **Windows GUI Features**

### **Professional Interface**
- **Native Windows look and feel** using tkinter
- **File browser integration** for easy A2L file selection
- **Real-time search** with progress indicators
- **Responsive design** that works on all screen sizes
- **Professional styling** with proper fonts and spacing

### **User Experience**
- **Browse button** to select A2L files easily
- **Search box** with Enter key support for quick searching
- **Description display** shows signal description immediately
- **"More Info" button** expands to show comprehensive signal details
- **Status bar** provides real-time feedback
- **Error handling** with user-friendly message boxes

### **Search Functionality**
- **Case-insensitive search** - works with any combination of upper/lowercase
- **Instant results** - description appears immediately when signal is found
- **Detailed information** - comprehensive signal data available on demand
- **Memory efficient** - handles large A2L files without performance issues

## 🚀 **How to Run**

### **Method 1: Double-click (Easiest)**
1. Double-click `run_gui.bat`
2. The GUI window will open automatically

### **Method 2: Python Command**
1. Open Command Prompt or PowerShell
2. Navigate to the application folder
3. Run: `python a2l_gui.py`

### **Method 3: Main Launcher**
1. Double-click `A2L_Signal_Search.py`
2. Or run: `python A2L_Signal_Search.py`

## 📋 **How to Use**

### **Step 1: Select A2L File**
1. Click the **"Browse..."** button
2. Navigate to your A2L file location
3. Select your `.a2l` file
4. The file path will appear in the text field

### **Step 2: Search for Signal**
1. Enter the signal name in the **"Signal Name"** field
2. Click **"Search"** button or press **Enter**
3. The application will search the file (progress shown in status bar)

### **Step 3: View Results**
1. **Signal name and type** appear at the top
2. **Description** is displayed immediately in the text area
3. Click **"More Info"** to see comprehensive details
4. Click **"Less Info"** to collapse the detailed view

## 🎯 **Interface Layout**

```
┌─────────────────────────────────────────────────────┐
│                A2L Signal Search                    │
├─────────────────────────────────────────────────────┤
│ A2L File Selection                                  │
│ File: [________________________] [Browse...]       │
├─────────────────────────────────────────────────────┤
│ Signal Search                                       │
│ Signal Name: [________________] [Search]            │
├─────────────────────────────────────────────────────┤
│ Status: Ready - Please select an A2L file          │
├─────────────────────────────────────────────────────┤
│ Search Results                                      │
│ Signal: eng_hour_ofs (MEASUREMENT)                  │
│                                                     │
│ Description:                                        │
│ ┌─────────────────────────────────────────────────┐ │
│ │ LIF NVRAM, ECU replacement offset for ENG HOUR │ │
│ │                                                 │ │
│ └─────────────────────────────────────────────────┘ │
│                                                     │
│ [More Info]                                         │
│                                                     │
│ Signal Details (when expanded)                      │
│ ┌─────────────────────────────────────────────────┐ │
│ │ Signal Name: eng_hour_ofs                       │ │
│ │ Type: MEASUREMENT                               │ │
│ │ Data Type: UWORD                                │ │
│ │ ECU Address: 0x40005c70                         │ │
│ │ Display Identifier: ENG_HOUR_OFS                │ │
│ │ ...                                             │ │
│ └─────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────┘
```

## 📁 **Files for Distribution**

### **Essential Files (Required)**
- `a2l_gui.py` - Main GUI application
- `A2L_Signal_Search.py` - Launcher script
- `run_gui.bat` - Windows batch file

### **Optional Files**
- `test_gui.py` - Test script
- `GUI_README.md` - This documentation

### **For Your Teammates**
To distribute to your teammates, provide them with:
1. `a2l_gui.py`
2. `run_gui.bat`
3. `GUI_README.md`

They just need to double-click `run_gui.bat` to start the application!

## 🔧 **Requirements**

- **Python 3.6 or higher** (with tkinter - included by default)
- **Windows OS** (tested on Windows 10/11)
- **No additional packages required** - uses only Python standard library

## 📊 **Signal Information Displayed**

### **Immediate Display (Description View)**
- Signal name and type
- Signal description

### **Detailed View (More Info)**
- **For MEASUREMENT signals:**
  - Data type (UBYTE, UWORD, ULONG, etc.)
  - ECU Address
  - Display identifier
  - Conversion method
  - Value ranges
  - Raw content

- **For CHARACTERISTIC signals:**
  - Characteristic type (VALUE, CURVE, MAP, etc.)
  - Address
  - Display identifier
  - Conversion method
  - Bit mask (if applicable)
  - Value ranges
  - Raw content

### **Commented Out (For Future Use)**
- Resolution
- Accuracy  
- Format

## 🎨 **GUI Features**

### **Professional Styling**
- Clean, modern interface design
- Proper font sizing and spacing
- Intuitive button placement
- Status feedback for all operations

### **User-Friendly Features**
- **File browser** - No need to type file paths
- **Enter key support** - Press Enter to search
- **Scrollable text areas** - Handle long descriptions and details
- **Expandable sections** - Show/hide details as needed
- **Status updates** - Always know what's happening
- **Error messages** - Clear feedback when something goes wrong

### **Performance Features**
- **Threaded search** - GUI stays responsive during search
- **Memory efficient** - Streams through large files
- **Fast search** - Stops immediately when signal is found
- **Progress indicators** - Shows search progress for large files

## 🚀 **Distribution to Teammates**

### **Simple Distribution Package**
Create a folder with these files:
```
A2L_Signal_Search/
├── a2l_gui.py
├── run_gui.bat
└── GUI_README.md
```

### **Instructions for Teammates**
1. **Extract** the folder to any location on their computer
2. **Double-click** `run_gui.bat` to start the application
3. **Browse** for their A2L file
4. **Search** for signals!

### **No Installation Required**
- No complex setup or installation
- Works with any Python 3.6+ installation
- Uses only built-in Python libraries
- Portable - can run from any folder

## 🆘 **Troubleshooting**

### **Application Won't Start**
- Ensure Python 3.6+ is installed
- Check that `a2l_gui.py` is in the same folder as `run_gui.bat`
- Try running `python a2l_gui.py` from command line for error details

### **Search Not Working**
- Verify the A2L file is selected correctly
- Check signal name spelling
- Try different case combinations (uppercase/lowercase)
- Ensure the A2L file is not corrupted

### **GUI Looks Wrong**
- This is normal on some systems with different DPI settings
- The application will still function correctly
- Try adjusting Windows display scaling if needed

## ✅ **Success Indicators**

You'll know the application is working correctly when:
- ✅ GUI window opens without errors
- ✅ Browse button opens file dialog
- ✅ Selected file path appears in the text field
- ✅ Search finds signals and displays descriptions
- ✅ "More Info" button shows detailed information
- ✅ Status bar provides helpful feedback

## 🎉 **Ready for Distribution!**

Your A2L Signal Search Windows GUI application is ready to distribute to your teammates. They'll have a professional, easy-to-use tool for searching A2L signals with just a few clicks!
