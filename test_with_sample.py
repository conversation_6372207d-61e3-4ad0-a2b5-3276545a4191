#!/usr/bin/env python3
"""
Test the A2L search functionality with a sample A2L content
"""

import os
import tempfile
from main import search_signal_in_a2l, print_signal_details

def create_sample_a2l():
    """Create a sample A2L file for testing."""
    sample_content = '''
/* Sample A2L file for testing */
ASAP2_VERSION 1 41
/begin PROJECT
   TEST_PROJECT
   "Test Project"
   
   /begin MODULE
      TEST_MODULE
      "Test Module"
      
      /begin MEASUREMENT eng_hour_ofs
         "LIF NVRAM, ECU replacement offset for ENG HOUR"
         UWORD
         _CNV_A_R_LINEAR_____0_CM
         1
         100.
         0.
         6553.5
         DISPLAY_IDENTIFIER ENG_HOUR_OFS
         ECU_ADDRESS 0x40005c70
         FORMAT "%6.1"
         /begin IF_DATA XCP
            /begin DAQ_EVENT
               VARIABLE
               /begin DEFAULT_EVENT_LIST
                  EVENT 6
               /end DEFAULT_EVENT_LIST
            /end DAQ_EVENT
         /end IF_DATA
      /end MEASUREMENT

      /begin MEASUREMENT abc_conv_mon
         "Anti-bounce counter for ADC error"
         UBYTE
         _CNV_A_R_LINEAR_____3_CM
         1
         100.
         0.
         255.
         DISPLAY_IDENTIFIER ABC_CONV_MON
         ECU_ADDRESS 0x40005248
         FORMAT "%3.0"
      /end MEASUREMENT

      /begin CHARACTERISTIC c_abc_inc_conv_mon
         "Anti bounce counter increment"
         VALUE
         0x80005248
         _REC_S1VAL_20_U1
         255.
         _CNV_A_R_LINEAR_____3_CM
         0.
         255.
         DISPLAY_IDENTIFIER C_ABC_INC_CONV_MON
         FORMAT "%3.0"
      /end CHARACTERISTIC

      /begin CHARACTERISTIC lc_test_flag
         "Test flag for demonstration"
         VALUE
         0x80004eef
         _REC_S1VAL_20_U1
         1.
         _CNV_S_2_RANGE______2_CM
         0.
         1.
         DISPLAY_IDENTIFIER LC_TEST_FLAG
         BIT_MASK 0x1
      /end CHARACTERISTIC

   /end MODULE
/end PROJECT
'''
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.a2l', delete=False, encoding='utf-8') as f:
        f.write(sample_content)
        return f.name

def test_search_functionality():
    """Test the search functionality with sample data."""
    print("Creating sample A2L file...")
    sample_file = create_sample_a2l()
    
    try:
        print(f"Sample A2L file created: {sample_file}")
        print("=" * 60)
        
        # Test cases
        test_signals = [
            'eng_hour_ofs',      # Should find MEASUREMENT
            'ENG_HOUR_OFS',      # Test case insensitive
            'abc_conv_mon',      # Should find MEASUREMENT
            'c_abc_inc_conv_mon', # Should find CHARACTERISTIC
            'lc_test_flag',      # Should find CHARACTERISTIC with BIT_MASK
            'nonexistent_signal' # Should not find
        ]
        
        for signal in test_signals:
            print(f"\nTesting search for: '{signal}'")
            print("-" * 40)
            
            result = search_signal_in_a2l(signal, sample_file)
            
            if result:
                print(f"✓ Found signal: {result['name']}")
                print(f"  Type: {result['type']}")
                print(f"  Description: {result['description']}")
                
                if result['type'] == 'MEASUREMENT':
                    if result['data_type']:
                        print(f"  Data Type: {result['data_type']}")
                    if result['ecu_address']:
                        print(f"  ECU Address: {result['ecu_address']}")
                
                elif result['type'] == 'CHARACTERISTIC':
                    if result['characteristic_type']:
                        print(f"  Characteristic Type: {result['characteristic_type']}")
                    if result['address']:
                        print(f"  Address: {result['address']}")
                    if result['bit_mask']:
                        print(f"  Bit Mask: {result['bit_mask']}")
                
                if result['display_identifier']:
                    print(f"  Display Identifier: {result['display_identifier']}")
                if result['format']:
                    print(f"  Format: {result['format']}")
            else:
                print(f"✗ Signal '{signal}' not found")
        
        print("\n" + "=" * 60)
        print("Testing detailed output for 'eng_hour_ofs':")
        print("=" * 60)
        
        result = search_signal_in_a2l('eng_hour_ofs', sample_file)
        if result:
            print_signal_details(result)
        
        print("\nAll tests completed successfully!")
        
    finally:
        # Clean up
        if os.path.exists(sample_file):
            os.unlink(sample_file)
            print(f"\nCleaned up temporary file: {sample_file}")

if __name__ == "__main__":
    test_search_functionality()
