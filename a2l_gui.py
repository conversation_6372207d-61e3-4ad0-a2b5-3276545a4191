#!/usr/bin/env python3
"""
A2L Signal Search - Windows GUI Application

A desktop GUI application for searching signals in A2L files.
Features:
- File browser for A2L file selection
- Signal search with case-insensitive matching
- Description display with expandable details
- Clean, professional Windows interface
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import re
from typing import Dict, Optional
import threading


class A2LSignalSearchGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("A2L Signal Search")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        
        # Variables
        self.a2l_file_path = tk.StringVar()
        self.search_signal_var = tk.StringVar()
        self.current_signal_details = None
        
        # Configure style
        self.setup_styles()
        
        # Create GUI
        self.create_widgets()
        
        # Center window
        self.center_window()
    
    def setup_styles(self):
        """Setup custom styles for the application."""
        style = ttk.Style()
        
        # Configure styles
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Info.TLabel', font=('Arial', 10))
        style.configure('Search.TButton', font=('Arial', 10, 'bold'))
        style.configure('Browse.TButton', font=('Arial', 9))
        
    def center_window(self):
        """Center the window on the screen."""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """Create and layout all GUI widgets."""
        # Main container
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="A2L Signal Search", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # File selection section
        file_frame = ttk.LabelFrame(main_frame, text="A2L File Selection", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Label(file_frame, text="File:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        self.file_entry = ttk.Entry(file_frame, textvariable=self.a2l_file_path, state='readonly')
        self.file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        self.browse_button = ttk.Button(file_frame, text="Browse...", command=self.browse_file, style='Browse.TButton')
        self.browse_button.grid(row=0, column=2, sticky=tk.W)
        
        # Search section
        search_frame = ttk.LabelFrame(main_frame, text="Signal Search", padding="10")
        search_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        search_frame.columnconfigure(1, weight=1)
        
        ttk.Label(search_frame, text="Signal Name:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_signal_var, font=('Arial', 10))
        self.search_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.search_entry.bind('<Return>', lambda e: self.search_signal())
        
        self.search_button = ttk.Button(search_frame, text="Search", command=self.search_signal, style='Search.TButton')
        self.search_button.grid(row=0, column=2, sticky=tk.W)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready - Please select an A2L file")
        self.status_label = ttk.Label(main_frame, textvariable=self.status_var, style='Info.TLabel', foreground='blue')
        self.status_label.grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=(0, 10))
        
        # Results section
        results_frame = ttk.LabelFrame(main_frame, text="Search Results", padding="10")
        results_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(2, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # Signal name display
        self.signal_name_var = tk.StringVar()
        self.signal_name_label = ttk.Label(results_frame, textvariable=self.signal_name_var, style='Heading.TLabel')
        self.signal_name_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        # Description display
        desc_label = ttk.Label(results_frame, text="Description:", style='Info.TLabel')
        desc_label.grid(row=1, column=0, sticky=tk.W, pady=(5, 5))
        
        self.description_text = scrolledtext.ScrolledText(results_frame, height=4, wrap=tk.WORD, font=('Arial', 10))
        self.description_text.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        self.description_text.config(state=tk.DISABLED)
        
        # More info button
        self.more_info_button = ttk.Button(results_frame, text="More Info", command=self.toggle_details, state=tk.DISABLED)
        self.more_info_button.grid(row=3, column=0, sticky=tk.W, pady=(0, 10))
        
        # Details section (initially hidden)
        self.details_frame = ttk.LabelFrame(results_frame, text="Signal Details", padding="10")
        self.details_text = scrolledtext.ScrolledText(self.details_frame, height=8, wrap=tk.WORD, font=('Courier', 9))
        self.details_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.details_frame.columnconfigure(0, weight=1)
        self.details_frame.rowconfigure(0, weight=1)
        self.details_text.config(state=tk.DISABLED)
        
        # Initially hide results
        self.hide_results()
    
    def browse_file(self):
        """Open file dialog to select A2L file."""
        file_path = filedialog.askopenfilename(
            title="Select A2L File",
            filetypes=[("A2L files", "*.a2l *.A2L"), ("All files", "*.*")]
        )
        
        if file_path:
            self.a2l_file_path.set(file_path)
            self.status_var.set(f"File loaded: {os.path.basename(file_path)}")
            self.hide_results()
    
    def search_signal(self):
        """Search for the specified signal in the A2L file."""
        if not self.a2l_file_path.get():
            messagebox.showerror("Error", "Please select an A2L file first.")
            return
        
        signal_name = self.search_signal_var.get().strip()
        if not signal_name:
            messagebox.showerror("Error", "Please enter a signal name to search.")
            return
        
        # Disable search button and show progress
        self.search_button.config(state=tk.DISABLED)
        self.status_var.set("Searching...")
        self.root.update()
        
        # Perform search in a separate thread to keep GUI responsive
        threading.Thread(target=self._perform_search, args=(signal_name,), daemon=True).start()
    
    def _perform_search(self, signal_name):
        """Perform the actual search operation."""
        try:
            result = self.search_signal_in_a2l(signal_name, self.a2l_file_path.get())
            
            # Update GUI in main thread
            self.root.after(0, self._update_search_results, result, signal_name)
            
        except Exception as e:
            self.root.after(0, self._handle_search_error, str(e))
    
    def _update_search_results(self, result, signal_name):
        """Update GUI with search results."""
        self.search_button.config(state=tk.NORMAL)
        
        if result:
            self.current_signal_details = result
            self.display_signal_result(result)
            self.status_var.set(f"Found signal: {result['name']}")
        else:
            self.hide_results()
            self.status_var.set(f"Signal '{signal_name}' not found")
            messagebox.showinfo("Not Found", f"Signal '{signal_name}' was not found in the A2L file.")
    
    def _handle_search_error(self, error_msg):
        """Handle search errors."""
        self.search_button.config(state=tk.NORMAL)
        self.status_var.set("Search failed")
        messagebox.showerror("Search Error", f"An error occurred during search:\n{error_msg}")
    
    def display_signal_result(self, signal_info):
        """Display the search result in the GUI."""
        # Show signal name
        self.signal_name_var.set(f"Signal: {signal_info['name']} ({signal_info['type']})")
        
        # Show description
        self.description_text.config(state=tk.NORMAL)
        self.description_text.delete(1.0, tk.END)
        description = signal_info.get('description', 'No description available')
        self.description_text.insert(1.0, description)
        self.description_text.config(state=tk.DISABLED)
        
        # Enable more info button
        self.more_info_button.config(state=tk.NORMAL)
        
        # Show results section
        self.show_results()
        
        # Hide details initially
        self.details_frame.grid_remove()
        self.more_info_button.config(text="More Info")
    
    def toggle_details(self):
        """Toggle the display of detailed signal information."""
        if self.details_frame.winfo_viewable():
            # Hide details
            self.details_frame.grid_remove()
            self.more_info_button.config(text="More Info")
        else:
            # Show details
            self.display_signal_details()
            self.details_frame.grid(row=4, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
            self.more_info_button.config(text="Less Info")
    
    def display_signal_details(self):
        """Display detailed signal information."""
        if not self.current_signal_details:
            return
        
        details = self.current_signal_details
        
        # Build details text
        details_text = f"Signal Name: {details['name']}\n"
        details_text += f"Type: {details['type']}\n"
        details_text += f"Description: {details.get('description', 'N/A')}\n\n"
        
        if details['type'] == 'MEASUREMENT':
            if details.get('data_type'):
                details_text += f"Data Type: {details['data_type']}\n"
            if details.get('ecu_address'):
                details_text += f"ECU Address: {details['ecu_address']}\n"
        
        elif details['type'] == 'CHARACTERISTIC':
            if details.get('characteristic_type'):
                details_text += f"Characteristic Type: {details['characteristic_type']}\n"
            if details.get('address'):
                details_text += f"Address: {details['address']}\n"
            if details.get('bit_mask'):
                details_text += f"Bit Mask: {details['bit_mask']}\n"
        
        if details.get('display_identifier'):
            details_text += f"Display Identifier: {details['display_identifier']}\n"
        
        if details.get('conversion'):
            details_text += f"Conversion Method: {details['conversion']}\n"
        
        if details.get('lower_limit') and details.get('upper_limit'):
            details_text += f"Range: {details['lower_limit']} to {details['upper_limit']}\n"
        
        # Add raw content
        details_text += f"\nRaw Content:\n{'-'*40}\n"
        raw_content = details.get('raw_content', 'No raw content available')
        details_text += raw_content[:1000] + ("..." if len(raw_content) > 1000 else "")
        
        # Update details display
        self.details_text.config(state=tk.NORMAL)
        self.details_text.delete(1.0, tk.END)
        self.details_text.insert(1.0, details_text)
        self.details_text.config(state=tk.DISABLED)
    
    def show_results(self):
        """Show the results section."""
        # Results are always visible, just update content
        pass
    
    def hide_results(self):
        """Hide/clear the results section."""
        self.signal_name_var.set("")
        self.description_text.config(state=tk.NORMAL)
        self.description_text.delete(1.0, tk.END)
        self.description_text.config(state=tk.DISABLED)
        self.more_info_button.config(state=tk.DISABLED)
        self.details_frame.grid_remove()
        self.current_signal_details = None

    def search_signal_in_a2l(self, signal_name: str, file_path: str) -> Optional[Dict]:
        """Search for a specific signal in the A2L file efficiently."""
        signal_name_lower = signal_name.lower()

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                current_section = None
                current_signal = None
                signal_content = []
                in_target_signal = False

                for line in file:
                    line = line.strip()

                    # Check for beginning of MEASUREMENT or CHARACTERISTIC
                    # Updated regex to handle signal names with double underscores and whitespace issues
                    measurement_match = re.match(r'/begin\s+MEASUREMENT\s+([^\s]+)', line, re.IGNORECASE)
                    characteristic_match = re.match(r'/begin\s+CHARACTERISTIC\s+([^\s]+)', line, re.IGNORECASE)

                    if measurement_match:
                        current_section = 'MEASUREMENT'
                        current_signal = measurement_match.group(1).strip()  # Strip whitespace
                        signal_content = []
                        in_target_signal = (current_signal.lower() == signal_name_lower)
                        continue

                    elif characteristic_match:
                        current_section = 'CHARACTERISTIC'
                        current_signal = characteristic_match.group(1).strip()  # Strip whitespace
                        signal_content = []
                        in_target_signal = (current_signal.lower() == signal_name_lower)
                        continue

                    # Check for end of section
                    if re.match(r'/end\s+(MEASUREMENT|CHARACTERISTIC)', line, re.IGNORECASE):
                        if in_target_signal and current_signal:
                            # Found our target signal, extract information
                            content_str = '\n'.join(signal_content)
                            if current_section == 'MEASUREMENT':
                                return self.extract_measurement_info(current_signal, content_str)
                            else:
                                return self.extract_characteristic_info(current_signal, content_str)

                        current_section = None
                        current_signal = None
                        signal_content = []
                        in_target_signal = False
                        continue

                    # Collect content if we're in the target signal
                    if in_target_signal:
                        signal_content.append(line)

            return None

        except Exception as e:
            raise Exception(f"Error reading file: {e}")

    def extract_measurement_info(self, signal_name: str, content: str) -> Dict:
        """Extract information from a MEASUREMENT block."""
        info = {
            'name': signal_name,
            'type': 'MEASUREMENT',
            'description': '',
            'data_type': '',
            'conversion': '',
            'lower_limit': '',
            'upper_limit': '',
            'display_identifier': '',
            'ecu_address': '',
            'raw_content': content.strip()
        }

        # Extract description (usually the first quoted string)
        desc_match = re.search(r'"([^"]*)"', content)
        if desc_match:
            info['description'] = desc_match.group(1)

        # Extract data type
        data_type_match = re.search(r'(UBYTE|SBYTE|UWORD|SWORD|ULONG|SLONG|FLOAT32_IEEE)', content)
        if data_type_match:
            info['data_type'] = data_type_match.group(1)

        # Extract common fields
        self.extract_common_fields(info, content)

        return info

    def extract_characteristic_info(self, signal_name: str, content: str) -> Dict:
        """Extract information from a CHARACTERISTIC block."""
        info = {
            'name': signal_name,
            'type': 'CHARACTERISTIC',
            'description': '',
            'characteristic_type': '',
            'address': '',
            'conversion': '',
            'lower_limit': '',
            'upper_limit': '',
            'display_identifier': '',
            'bit_mask': '',
            'raw_content': content.strip()
        }

        # Extract description (usually the first quoted string)
        desc_match = re.search(r'"([^"]*)"', content)
        if desc_match:
            info['description'] = desc_match.group(1)

        # Extract characteristic type
        char_type_match = re.search(r'(VALUE|CURVE|MAP|CUBOID|CUBE_4|CUBE_5)', content)
        if char_type_match:
            info['characteristic_type'] = char_type_match.group(1)

        # Extract address (hex value)
        addr_match = re.search(r'0x[0-9a-fA-F]+', content)
        if addr_match:
            info['address'] = addr_match.group(0)

        # Extract bit mask if present
        bit_mask_match = re.search(r'BIT_MASK\s+(0x[0-9a-fA-F]+)', content)
        if bit_mask_match:
            info['bit_mask'] = bit_mask_match.group(1)

        # Extract common fields
        self.extract_common_fields(info, content)

        return info

    def extract_common_fields(self, info: Dict, content: str):
        """Extract common fields present in both MEASUREMENT and CHARACTERISTIC."""
        # Extract DISPLAY_IDENTIFIER
        # Updated regex to handle identifiers with double underscores
        display_id_match = re.search(r'DISPLAY_IDENTIFIER\s+([A-Za-z0-9_]+)', content)
        if display_id_match:
            info['display_identifier'] = display_id_match.group(1)

        # Extract ECU_ADDRESS
        ecu_addr_match = re.search(r'ECU_ADDRESS\s+(0x[0-9a-fA-F]+)', content)
        if ecu_addr_match:
            info['ecu_address'] = ecu_addr_match.group(1)

        # Extract conversion method
        conv_match = re.search(r'_CNV_[A-Z0-9_]+', content)
        if conv_match:
            info['conversion'] = conv_match.group(0)

        # Extract numeric values (limits)
        numbers = re.findall(r'\b\d+\.?\d*\b', content)
        if len(numbers) >= 3:
            info['lower_limit'] = numbers[2] if len(numbers) > 2 else ''
            info['upper_limit'] = numbers[3] if len(numbers) > 3 else ''


def main():
    """Main function to start the GUI application."""
    root = tk.Tk()
    app = A2LSignalSearchGUI(root)

    # Set window icon (if available)
    try:
        root.iconbitmap('icon.ico')  # Add an icon file if you have one
    except:
        pass  # Ignore if icon file not found

    # Start the GUI event loop
    root.mainloop()


if __name__ == "__main__":
    main()
