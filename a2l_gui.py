#!/usr/bin/env python3
"""
A2L Signal Search - Windows GUI Application

A desktop GUI application for searching signals in A2L files.
Features:
- File browser for A2L file selection
- Signal search with case-insensitive matching
- Description display with expandable details
- Clean, professional Windows interface
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import re
from typing import Dict, Optional, List, Tuple
import threading
import difflib


class A2LSignalSearchGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Flash View - Label Search Engine")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        
        # Variables
        self.a2l_file_path = tk.StringVar()
        self.search_signal_var = tk.StringVar()
        self.current_signal_details = None

        # Search engine variables
        self.all_signals = []  # List of all signal names from A2L file
        self.signal_index = {}  # Dictionary mapping signal names to their details
        self.suggestions_listbox = None
        self.suggestions_frame = None
        self.is_loading_signals = False

        # Clipboard monitoring variables
        self.clipboard_monitoring_active = False
        self.last_clipboard_content = ""
        self.clipboard_check_job = None

        # Popup management
        self.current_popup = None
        
        # Configure style
        self.setup_styles()
        
        # Create GUI
        self.create_widgets()

        # Center window
        self.center_window()

        # Setup cleanup on window close
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Bind ESC key to close popups
        self.root.bind('<Escape>', self.on_escape_key)
    
    def setup_styles(self):
        """Setup custom styles for the application."""
        style = ttk.Style()
        
        # Configure styles
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Info.TLabel', font=('Arial', 10))
        style.configure('Search.TButton', font=('Arial', 10, 'bold'))
        style.configure('Browse.TButton', font=('Arial', 9))
        
    def center_window(self):
        """Center the window on the screen."""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """Create and layout all GUI widgets."""
        # Main container
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Header section with app name and tagline
        header_frame = ttk.Frame(main_frame)
        header_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 20))
        header_frame.columnconfigure(0, weight=1)

        # App name
        app_name_label = ttk.Label(header_frame, text="Flash View",
                                  font=('Arial', 18, 'bold'), foreground='#2E86AB')
        app_name_label.grid(row=0, column=0)

        # Tagline
        tagline_label = ttk.Label(header_frame, text="Label Search Engine",
                                 font=('Arial', 10, 'italic'), foreground='#666666')
        tagline_label.grid(row=1, column=0, pady=(0, 5))
        
        # File selection section
        file_frame = ttk.LabelFrame(main_frame, text="A2L File Selection", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Label(file_frame, text="File:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        self.file_entry = ttk.Entry(file_frame, textvariable=self.a2l_file_path, state='readonly')
        self.file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        self.browse_button = ttk.Button(file_frame, text="Browse...", command=self.browse_file, style='Browse.TButton')
        self.browse_button.grid(row=0, column=2, sticky=tk.W)

        # Filter section
        filter_frame = ttk.LabelFrame(main_frame, text="Filter by Object Type", padding="10")
        filter_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # Filter checkboxes
        self.filter_select_all = tk.BooleanVar(value=True)
        self.filter_measurement = tk.BooleanVar(value=True)
        self.filter_char_map = tk.BooleanVar(value=True)
        self.filter_char_val = tk.BooleanVar(value=True)
        self.filter_char_cur = tk.BooleanVar(value=True)
        self.filter_axis_pts = tk.BooleanVar(value=True)
        self.filter_other = tk.BooleanVar(value=True)

        # Row 0: Select All
        ttk.Checkbutton(filter_frame, text="SELECT ALL", variable=self.filter_select_all,
                       command=self.on_select_all_change, style='Bold.TCheckbutton').grid(row=0, column=0, sticky=tk.W, padx=(0, 20))

        # Row 1: Main categories
        ttk.Checkbutton(filter_frame, text="MEASUREMENT", variable=self.filter_measurement,
                       command=self.on_filter_change).grid(row=1, column=0, sticky=tk.W, padx=(0, 15))
        ttk.Checkbutton(filter_frame, text="AXIS_PTS", variable=self.filter_axis_pts,
                       command=self.on_filter_change).grid(row=1, column=1, sticky=tk.W, padx=(0, 15))
        ttk.Checkbutton(filter_frame, text="OTHER", variable=self.filter_other,
                       command=self.on_filter_change).grid(row=1, column=2, sticky=tk.W, padx=(0, 15))

        # Row 2: CHARACTERISTIC sub-types
        ttk.Label(filter_frame, text="CHARACTERISTIC Types:", font=('Arial', 9, 'bold')).grid(row=2, column=0, sticky=tk.W, pady=(10, 5))
        ttk.Checkbutton(filter_frame, text="MAP (Maps)", variable=self.filter_char_map,
                       command=self.on_filter_change).grid(row=3, column=0, sticky=tk.W, padx=(20, 15))
        ttk.Checkbutton(filter_frame, text="VAL (Scalars)", variable=self.filter_char_val,
                       command=self.on_filter_change).grid(row=3, column=1, sticky=tk.W, padx=(0, 15))
        ttk.Checkbutton(filter_frame, text="CUR (Curves)", variable=self.filter_char_cur,
                       command=self.on_filter_change).grid(row=3, column=2, sticky=tk.W, padx=(0, 15))

        # Search section
        search_frame = ttk.LabelFrame(main_frame, text="Signal Search", padding="10")
        search_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        search_frame.columnconfigure(1, weight=1)
        
        ttk.Label(search_frame, text="Signal Name:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_signal_var, font=('Arial', 10))
        self.search_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.search_entry.bind('<Return>', self.on_search_entry_return)
        self.search_entry.bind('<KeyRelease>', self.on_search_key_release)
        self.search_entry.bind('<FocusOut>', self.on_search_focus_out)
        self.search_entry.bind('<Down>', self.on_arrow_down)
        self.search_entry.bind('<Up>', self.on_arrow_up)

        # Search status indicator (replaces search button)
        self.search_status_label = ttk.Label(search_frame, text="Type to search...", font=('Arial', 9), foreground='gray')
        self.search_status_label.grid(row=0, column=2, sticky=tk.W, padx=(10, 0))

        # Search help label
        help_text = "💡 Tip: Use * for wildcards • Click suggestions to search • Enter selects first suggestion"
        self.help_label = ttk.Label(search_frame, text=help_text, font=('Arial', 8), foreground='gray')
        self.help_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))

        # Clipboard monitoring section
        clipboard_frame = ttk.LabelFrame(main_frame, text="Clipboard Monitor", padding="10")
        clipboard_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        clipboard_frame.columnconfigure(1, weight=1)

        # Clipboard monitoring toggle
        self.clipboard_monitoring_var = tk.BooleanVar()
        self.clipboard_toggle = ttk.Checkbutton(clipboard_frame,
                                              text="Auto-detect signals from clipboard",
                                              variable=self.clipboard_monitoring_var,
                                              command=self.toggle_clipboard_monitoring)
        self.clipboard_toggle.grid(row=0, column=0, sticky=tk.W)

        # Clipboard status indicator
        self.clipboard_status_var = tk.StringVar(value="Inactive")
        self.clipboard_status_label = ttk.Label(clipboard_frame, textvariable=self.clipboard_status_var,
                                              font=('Arial', 9), foreground='gray')
        self.clipboard_status_label.grid(row=0, column=1, sticky=tk.E, padx=(10, 0))

        # Clipboard help text
        clipboard_help = "When activated, copying signal names will automatically show their details"
        self.clipboard_help_label = ttk.Label(clipboard_frame, text=clipboard_help,
                                            font=('Arial', 8), foreground='gray')
        self.clipboard_help_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))

        # Suggestions frame (initially hidden)
        self.suggestions_frame = ttk.Frame(main_frame)
        self.suggestions_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        self.suggestions_frame.columnconfigure(0, weight=1)

        # Suggestions listbox with descriptions - larger with horizontal scrolling
        self.suggestions_listbox = tk.Listbox(self.suggestions_frame, height=15, font=('Consolas', 10),
                                            selectmode=tk.SINGLE, activestyle='dotbox', width=120)
        self.suggestions_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Bind selection events
        self.suggestions_listbox.bind('<Button-1>', self.on_suggestion_click)
        self.suggestions_listbox.bind('<Double-Button-1>', self.on_suggestion_double_click)
        self.suggestions_listbox.bind('<Return>', self.on_suggestion_return)
        self.suggestions_listbox.bind('<Up>', self.on_listbox_up)
        self.suggestions_listbox.bind('<Down>', self.on_listbox_down)
        self.suggestions_listbox.bind('<Escape>', self.on_listbox_escape)

        # Vertical scrollbar
        v_scrollbar = ttk.Scrollbar(self.suggestions_frame, orient=tk.VERTICAL, command=self.suggestions_listbox.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.suggestions_listbox.configure(yscrollcommand=v_scrollbar.set)

        # Horizontal scrollbar for full description viewing
        h_scrollbar = ttk.Scrollbar(self.suggestions_frame, orient=tk.HORIZONTAL, command=self.suggestions_listbox.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.suggestions_listbox.configure(xscrollcommand=h_scrollbar.set)

        # Configure grid weights for proper resizing
        self.suggestions_frame.columnconfigure(0, weight=1)
        self.suggestions_frame.rowconfigure(0, weight=1)

        # Hide suggestions initially
        self.suggestions_frame.grid_remove()

        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready - Please select an A2L file")
        self.status_label = ttk.Label(main_frame, textvariable=self.status_var, style='Info.TLabel', foreground='blue')
        self.status_label.grid(row=6, column=0, columnspan=3, sticky=tk.W, pady=(0, 10))


        
        # Initially hide results
        self.hide_results()

    def toggle_clipboard_monitoring(self):
        """Toggle clipboard monitoring on/off."""
        if self.clipboard_monitoring_var.get():
            self.start_clipboard_monitoring()
        else:
            self.stop_clipboard_monitoring()

    def start_clipboard_monitoring(self):
        """Start monitoring clipboard for signal names."""
        if not self.a2l_file_path.get():
            messagebox.showwarning("Warning", "Please load an A2L file first before activating clipboard monitoring.")
            self.clipboard_monitoring_var.set(False)
            return

        self.clipboard_monitoring_active = True
        self.clipboard_status_var.set("Active")
        self.clipboard_status_label.config(foreground='green')

        # Get initial clipboard content
        try:
            self.last_clipboard_content = self.root.clipboard_get()
        except tk.TclError:
            self.last_clipboard_content = ""

        # Start monitoring
        self.check_clipboard()

    def stop_clipboard_monitoring(self):
        """Stop monitoring clipboard."""
        self.clipboard_monitoring_active = False
        self.clipboard_status_var.set("Inactive")
        self.clipboard_status_label.config(foreground='gray')

        # Cancel scheduled check
        if self.clipboard_check_job:
            self.root.after_cancel(self.clipboard_check_job)
            self.clipboard_check_job = None

    def check_clipboard(self):
        """Check clipboard for new content and detect signal names."""
        if not self.clipboard_monitoring_active:
            return

        try:
            current_clipboard = self.root.clipboard_get()

            # Check if clipboard content has changed
            if current_clipboard != self.last_clipboard_content:
                self.last_clipboard_content = current_clipboard

                # Clean and check if it's a potential signal name
                signal_candidate = current_clipboard.strip()

                # Basic validation: not too long, not too short, reasonable characters
                if (2 <= len(signal_candidate) <= 100 and
                    not '\n' in signal_candidate and
                    not '\t' in signal_candidate):

                    # Check if this signal exists in our A2L file
                    self.check_clipboard_signal(signal_candidate)

        except tk.TclError:
            # Clipboard access failed, ignore
            pass
        except Exception as e:
            print(f"Clipboard monitoring error: {e}")

        # Schedule next check
        if self.clipboard_monitoring_active:
            self.clipboard_check_job = self.root.after(500, self.check_clipboard)  # Check every 500ms

    def check_clipboard_signal(self, signal_candidate):
        """Check if clipboard content is a valid signal and show popup if found."""
        # Check exact match first (using the actual signal name from all_signals)
        for signal_name in self.all_signals:
            if signal_name.lower() == signal_candidate.lower():
                self.show_clipboard_signal_popup(signal_name)
                return

        # Check if signal exists in signal_index (fallback)
        if signal_candidate.lower() in self.signal_index:
            self.show_clipboard_signal_popup(signal_candidate)
            return

        # Check if it's a partial match (for copied partial signal names)
        matches = []
        for signal_name in self.all_signals:
            if signal_candidate.lower() in signal_name.lower():
                matches.append(signal_name)

        # If only one match, show it
        if len(matches) == 1:
            self.show_clipboard_signal_popup(matches[0])

    def show_clipboard_signal_popup(self, signal_name):
        """Show signal info popup for clipboard-detected signal."""
        # Update status to show clipboard detection
        self.clipboard_status_var.set(f"Detected: {signal_name}")
        self.clipboard_status_label.config(foreground='blue')

        # Show the signal info popup
        self.show_signal_info_popup(signal_name)

        # Reset status after a delay
        self.root.after(3000, lambda: self.clipboard_status_var.set("Active") if self.clipboard_monitoring_active else None)
        self.root.after(3000, lambda: self.clipboard_status_label.config(foreground='green') if self.clipboard_monitoring_active else None)

    def browse_file(self):
        """Open file dialog to select A2L file."""
        file_path = filedialog.askopenfilename(
            title="Select A2L File",
            filetypes=[("A2L files", "*.a2l *.A2L"), ("All files", "*.*")]
        )
        
        if file_path:
            self.a2l_file_path.set(file_path)
            self.status_var.set(f"File loaded: {os.path.basename(file_path)} - Indexing signals...")
            self.hide_results()
            self.hide_suggestions()

            # Start signal indexing in background
            threading.Thread(target=self._index_signals, args=(file_path,), daemon=True).start()
    
    def search_signal(self):
        """Search for the specified signal in the A2L file."""
        if not self.a2l_file_path.get():
            messagebox.showerror("Error", "Please select an A2L file first.")
            return
        
        signal_name = self.search_signal_var.get().strip()
        if not signal_name:
            messagebox.showerror("Error", "Please enter a signal name to search.")
            return
        
        # Update search status and show progress
        self.search_status_label.config(text="Searching...", foreground='blue')
        self.status_var.set("Searching...")
        self.root.update()
        
        # Perform search in a separate thread to keep GUI responsive
        threading.Thread(target=self._perform_search, args=(signal_name,), daemon=True).start()
    
    def _perform_search(self, signal_name):
        """Perform the actual search operation."""
        try:
            result = self.search_signal_in_a2l(signal_name, self.a2l_file_path.get())
            
            # Update GUI in main thread
            self.root.after(0, self._update_search_results, result, signal_name)
            
        except Exception as e:
            self.root.after(0, self._handle_search_error, str(e))
    
    def _update_search_results(self, result, signal_name):
        """Update GUI with search results."""
        if result:
            self.current_signal_details = result
            self.display_signal_result(result)
            self.status_var.set(f"Found signal: {result['name']}")
            self.search_status_label.config(text="✅ Found!", foreground='green')
        else:
            self.hide_results()
            self.status_var.set(f"Signal '{signal_name}' not found")
            self.search_status_label.config(text="❌ Not found", foreground='red')

            # Try to find similar signals using fuzzy matching
            similar_signals = self.find_similar_signals(signal_name)

            if similar_signals:
                self.search_status_label.config(text="🔍 Similar found", foreground='orange')
                self.show_similar_signals_dialog(signal_name, similar_signals)
            else:
                messagebox.showinfo("Not Found", f"Signal '{signal_name}' was not found in the A2L file.\nNo similar signals found.")
    
    def _handle_search_error(self, error_msg):
        """Handle search errors."""
        self.search_status_label.config(text="❌ Error", foreground='red')
        self.status_var.set("Search failed")
        messagebox.showerror("Search Error", f"An error occurred during search:\n{error_msg}")
    
    def display_signal_result(self, signal_info):
        """Display the search result - automatically show detailed popup."""
        # Automatically show detailed popup for the found signal
        self.show_signal_info_popup(signal_info['name'])

    def show_results(self):
        """Show the results section."""
        pass  # No results section anymore

    def hide_results(self):
        """Hide/clear the results section."""
        pass  # No results section anymore

    def _index_signals(self, file_path):
        """Index all signals from the A2L file for search suggestions."""
        try:
            self.is_loading_signals = True
            self.all_signals = []
            self.signal_index = {}

            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                content = file.read()

            # Find all A2L objects and extract CHARACTERISTIC sub-types
            self._index_a2l_objects(content)

            # Sort signals alphabetically
            self.all_signals.sort()

            # Update status in main thread
            self.root.after(0, self._update_indexing_complete)

            # Start efficient description extraction in background
            threading.Thread(target=self._extract_descriptions_batch, args=(content,), daemon=True).start()

        except Exception as e:
            self.root.after(0, self._handle_indexing_error, str(e))
        finally:
            self.is_loading_signals = False

    def _index_a2l_objects(self, content):
        """Index A2L objects and extract CHARACTERISTIC sub-types."""
        # Find all A2L objects
        a2l_object_pattern = r'/begin\s+(MEASUREMENT|CHARACTERISTIC|AXIS_PTS|COMPU_METHOD|COMPU_TAB|COMPU_VTAB|COMPU_VTAB_RANGE|GROUP|FUNCTION|MOD_PAR|MOD_COMMON)\s+([^\s]+)'
        a2l_objects = re.findall(a2l_object_pattern, content, re.IGNORECASE)

        # For CHARACTERISTIC objects, we need to extract their sub-type
        lines = content.split('\n')
        characteristic_subtypes = {}

        # Parse CHARACTERISTIC blocks to find their types
        current_char = None
        in_char_block = False

        for line in lines:
            line = line.strip()

            # Check for CHARACTERISTIC start
            char_match = re.match(r'/begin\s+CHARACTERISTIC\s+([^\s]+)', line, re.IGNORECASE)
            if char_match:
                current_char = char_match.group(1).strip()
                in_char_block = True
                continue

            # Check for CHARACTERISTIC end
            if re.match(r'/end\s+CHARACTERISTIC', line, re.IGNORECASE):
                current_char = None
                in_char_block = False
                continue

            # Look for characteristic type within the block
            if in_char_block and current_char:
                # Look for MAP, VAL, CUR patterns
                if re.search(r'\bMAP\b', line, re.IGNORECASE):
                    characteristic_subtypes[current_char.lower()] = 'MAP'
                elif re.search(r'\bVALUE\b', line, re.IGNORECASE):
                    characteristic_subtypes[current_char.lower()] = 'VAL'
                elif re.search(r'\bCURVE\b', line, re.IGNORECASE):
                    characteristic_subtypes[current_char.lower()] = 'CUR'

        # Add all objects to the index
        for object_type, signal_name in a2l_objects:
            signal_name = signal_name.strip()
            object_type = object_type.upper()

            # Determine the display type
            if object_type == 'CHARACTERISTIC':
                subtype = characteristic_subtypes.get(signal_name.lower(), 'VAL')  # Default to VAL
                display_type = f"CHAR_{subtype}"
                full_display = f"CHARACTERISTIC-{subtype}"
            else:
                display_type = object_type
                full_display = object_type

            self.all_signals.append(signal_name)
            self.signal_index[signal_name.lower()] = {
                'name': signal_name,
                'type': display_type,
                'full_type': full_display,
                'description': 'Loading description...'  # Placeholder for now
            }

    def _update_indexing_complete(self):
        """Update GUI when signal indexing is complete."""
        total_signals = len(self.all_signals)
        filename = os.path.basename(self.a2l_file_path.get())
        self.status_var.set(f"Ready - {filename} ({total_signals} signals indexed)")

    def _handle_indexing_error(self, error_msg):
        """Handle indexing errors."""
        self.status_var.set(f"Error indexing signals: {error_msg}")

    def _extract_descriptions_batch(self, content: str):
        """Extract descriptions for all signals efficiently in one pass."""
        try:
            lines = content.split('\n')
            descriptions = {}
            current_signal = None
            current_object_type = None
            in_signal_block = False

            # Single pass through the file to extract all descriptions
            for line in lines:
                line_stripped = line.strip()

                # Check for any A2L object start
                begin_match = re.match(r'/begin\s+(MEASUREMENT|CHARACTERISTIC|AXIS_PTS|COMPU_METHOD|COMPU_TAB|COMPU_VTAB|COMPU_VTAB_RANGE|GROUP|FUNCTION|MOD_PAR|MOD_COMMON)\s+([^\s]+)', line_stripped, re.IGNORECASE)
                if begin_match:
                    current_object_type = begin_match.group(1).upper()
                    current_signal = begin_match.group(2).strip()
                    in_signal_block = True
                    continue

                # Check for object end
                end_match = re.match(r'/end\s+(MEASUREMENT|CHARACTERISTIC|AXIS_PTS|COMPU_METHOD|COMPU_TAB|COMPU_VTAB|COMPU_VTAB_RANGE|GROUP|FUNCTION|MOD_PAR|MOD_COMMON)', line_stripped, re.IGNORECASE)
                if end_match and in_signal_block:
                    current_signal = None
                    current_object_type = None
                    in_signal_block = False
                    continue

                # Look for description within signal block
                if in_signal_block and current_signal:
                    desc_match = re.search(r'"([^"]*)"', line_stripped)
                    if desc_match:
                        description = desc_match.group(1).strip()
                        if description and len(description) > 3:  # Avoid very short descriptions
                            descriptions[current_signal.lower()] = description
                            # Found description, move to next signal
                            current_signal = None
                            in_signal_block = False

            # Update signal index with descriptions in main thread
            self.root.after(0, self._update_descriptions, descriptions)

        except Exception as e:
            print(f"Error extracting descriptions: {e}")

    def _update_descriptions(self, descriptions):
        """Update signal index with extracted descriptions."""
        try:
            for signal_key, description in descriptions.items():
                if signal_key in self.signal_index:
                    self.signal_index[signal_key]['description'] = description

            # Update status to show descriptions are loaded
            total_signals = len(self.all_signals)
            desc_count = len(descriptions)
            filename = os.path.basename(self.a2l_file_path.get())
            self.status_var.set(f"Ready - {filename} ({total_signals} signals, {desc_count} descriptions)")

        except Exception as e:
            print(f"Error updating descriptions: {e}")

    def on_search_key_release(self, event):
        """Handle key release events in search entry for auto-suggestions."""
        if event.keysym in ['Up', 'Down', 'Return', 'Tab']:
            return

        search_text = self.search_signal_var.get().strip()

        # Update search status based on input
        if not search_text:
            self.search_status_label.config(text="Type to search...", foreground='gray')
        elif len(search_text) < 2:
            self.search_status_label.config(text="Type 2+ characters...", foreground='orange')
        else:
            self.search_status_label.config(text="Select from suggestions ↓", foreground='green')

        if len(search_text) >= 2 and self.all_signals:  # Start suggesting after 2 characters
            self.show_suggestions(search_text)
        else:
            self.hide_suggestions()

    def show_suggestions(self, search_text):
        """Show search suggestions based on input text with wildcard and flexible matching."""
        if not self.all_signals:
            return

        search_lower = search_text.lower()
        suggestions = []

        # Check if this is a wildcard search (contains *)
        is_wildcard_search = '*' in search_text

        if is_wildcard_search:
            suggestions = self._wildcard_search(search_text)
        else:
            suggestions = self._standard_search(search_text)

        # Limit to 15 suggestions for better visibility
        suggestions = suggestions[:15]

        if suggestions:
            self.suggestions_listbox.delete(0, tk.END)
            for suggestion in suggestions:
                # Extract signal name from suggestion
                signal_name = suggestion.split(' (')[0]
                signal_info = self.signal_index.get(signal_name.lower(), {})

                # Get description
                description = signal_info.get('description', 'Loading description...')
                full_type = signal_info.get('full_type', 'Unknown')

                # Format with clean alignment using separators - NO TRUNCATION
                # Signal name (35 chars) │ Type (20 chars) │ Full Description (scrollable)
                formatted_line = f"{signal_name:<35} │ {full_type:<20} │ {description}"
                self.suggestions_listbox.insert(tk.END, formatted_line)

            # Show suggestions and pre-select first item
            self.suggestions_frame.grid()
            if self.suggestions_listbox.size() > 0:
                self.suggestions_listbox.selection_set(0)
                self.suggestions_listbox.activate(0)
        else:
            self.hide_suggestions()

    def _standard_search(self, search_text):
        """Standard search: starts with, then contains."""
        search_lower = search_text.lower()
        suggestions = []

        # Find exact prefix matches first
        for signal in self.all_signals:
            if signal.lower().startswith(search_lower):
                signal_info = self.signal_index[signal.lower()]
                signal_type = signal_info['type']
                if self._is_signal_type_allowed(signal_type):
                    display_type = signal_info.get('full_type', signal_type)
                    suggestions.append(f"{signal} ({display_type})")

        # If no prefix matches, find partial matches
        if not suggestions:
            for signal in self.all_signals:
                if search_lower in signal.lower():
                    signal_info = self.signal_index[signal.lower()]
                    signal_type = signal_info['type']
                    if self._is_signal_type_allowed(signal_type):
                        display_type = signal_info.get('full_type', signal_type)
                        suggestions.append(f"{signal} ({display_type})")

        return suggestions

    def _wildcard_search(self, search_text):
        """Wildcard search: supports * as wildcard and flexible word order."""
        suggestions = []

        # Remove * and split into search terms
        search_terms = [term.strip().lower() for term in search_text.replace('*', ' ').split() if term.strip()]

        if not search_terms:
            return suggestions

        # Score each signal based on how well it matches
        signal_scores = []

        for signal in self.all_signals:
            signal_lower = signal.lower()
            signal_info = self.signal_index[signal_lower]
            signal_type = signal_info['type']

            # Check if signal type is allowed by filters
            if not self._is_signal_type_allowed(signal_type):
                continue

            score = self._calculate_wildcard_score(signal_lower, search_terms)

            if score > 0:
                display_type = signal_info.get('full_type', signal_type)
                signal_scores.append((score, f"{signal} ({display_type})"))

        # Sort by score (highest first) and return suggestions
        signal_scores.sort(key=lambda x: x[0], reverse=True)
        suggestions = [suggestion for score, suggestion in signal_scores]

        return suggestions

    def _calculate_wildcard_score(self, signal_lower, search_terms):
        """Calculate how well a signal matches the search terms with improved algorithm."""
        if not search_terms:
            return 0

        score = 0
        signal_parts = signal_lower.replace('_', ' ').split()

        # Find positions of each search term in the signal
        term_positions = []
        terms_found = 0

        for i, term in enumerate(search_terms):
            best_position = -1
            best_score = 0
            term_found = False

            # First check each part of the signal (word boundaries)
            for part_idx, part in enumerate(signal_parts):
                if term in part:
                    term_found = True
                    part_score = 0

                    # Score based on match quality
                    if term == part:
                        part_score = 25  # Exact match - higher score
                    elif part.startswith(term):
                        part_score = 20  # Prefix match
                    elif part.endswith(term):
                        part_score = 18  # Suffix match
                    else:
                        part_score = 15   # Contains match

                    # Bonus for early position in signal
                    position_bonus = max(0, 8 - part_idx * 1)
                    part_score += position_bonus

                    # Keep track of best match for this term
                    if part_score > best_score:
                        best_score = part_score
                        best_position = part_idx

            # Also check if term appears anywhere in the full signal (fallback)
            if not term_found and term in signal_lower:
                term_found = True
                best_score = 5  # Higher fallback score
                best_position = signal_lower.find(term) / len(signal_lower)  # Normalized position

            if term_found:
                terms_found += 1
                score += best_score
                term_positions.append((i, best_position, best_score))

        # Strong bonus for finding all terms - this is crucial for wildcard search
        if terms_found == len(search_terms):
            score += 50  # Increased bonus
        else:
            # Heavy penalty for missing terms in wildcard search
            missing_terms = len(search_terms) - terms_found
            score -= missing_terms * 25

        # Check term order bonus (terms should appear in search order)
        if len(term_positions) >= 2:
            order_bonus = 0
            for i in range(len(term_positions) - 1):
                current_pos = term_positions[i][1]
                next_pos = term_positions[i + 1][1]

                if current_pos < next_pos:  # Correct order
                    order_bonus += 15  # Higher order bonus
                elif current_pos > next_pos:  # Wrong order
                    order_bonus -= 3   # Lower penalty for wrong order

            score += order_bonus

        # Proximity bonus (terms close together score higher)
        if len(term_positions) >= 2:
            positions = [pos[1] for pos in term_positions if isinstance(pos[1], int) and pos[1] >= 0]
            if len(positions) >= 2:
                max_distance = max(positions) - min(positions)
                if max_distance <= 2:  # Terms within 2 words of each other
                    score += 20
                elif max_distance <= 4:  # Terms within 4 words
                    score += 12
                elif max_distance <= 6:  # Terms within 6 words
                    score += 6

        # Bonus for signal starting with first search term
        if search_terms and signal_lower.startswith(search_terms[0]):
            score += 30  # Higher starting bonus

        # Bonus for signal ending with last search term
        if len(search_terms) > 1 and signal_lower.endswith(search_terms[-1]):
            score += 20

        return max(0, score)  # Don't return negative scores

    def hide_suggestions(self, event=None):
        """Hide the suggestions listbox."""
        self.suggestions_frame.grid_remove()

    def on_suggestion_select(self, event=None):
        """Handle selection of a suggestion - automatically search."""
        selection = self.suggestions_listbox.curselection()
        if selection:
            suggestion_text = self.suggestions_listbox.get(selection[0])
            # Extract signal name (before the first │ separator)
            signal_name = suggestion_text.split(' │')[0].strip()

            self.search_signal_var.set(signal_name)
            self.hide_suggestions()

            # Update search status
            self.search_status_label.config(text="Searching...", foreground='blue')

            # Automatically perform the search
            self.search_signal()

    def on_search_entry_return(self, event):
        """Handle Enter key in search entry."""
        # If suggestions are visible, select the first one
        if self.suggestions_frame.winfo_viewable() and self.suggestions_listbox.size() > 0:
            self.suggestions_listbox.selection_set(0)
            self.on_suggestion_select()
        else:
            # No suggestions, search directly
            search_text = self.search_signal_var.get().strip()
            if search_text:
                self.hide_suggestions()
                self.search_status_label.config(text="Searching...", foreground='blue')
                self.search_signal()

    def on_arrow_down(self, event):
        """Handle down arrow key in search entry."""
        if self.suggestions_frame.winfo_viewable():
            self.suggestions_listbox.focus_set()
            self.suggestions_listbox.selection_set(0)
            return 'break'

    def on_arrow_up(self, event):
        """Handle up arrow key in search entry."""
        if self.suggestions_frame.winfo_viewable():
            self.suggestions_listbox.focus_set()
            last_index = self.suggestions_listbox.size() - 1
            if last_index >= 0:
                self.suggestions_listbox.selection_set(last_index)
            return 'break'

    def on_listbox_up(self, event):
        """Handle up arrow in suggestions listbox."""
        current_selection = self.suggestions_listbox.curselection()
        if current_selection:
            current_index = current_selection[0]
            if current_index == 0:
                # At top, go back to search entry
                self.search_entry.focus_set()
                self.search_entry.icursor(tk.END)
                return 'break'
            else:
                # Move up in the list
                new_index = current_index - 1
                self.suggestions_listbox.selection_clear(0, tk.END)
                self.suggestions_listbox.selection_set(new_index)
                self.suggestions_listbox.activate(new_index)
                self.suggestions_listbox.see(new_index)
                return 'break'

    def on_listbox_down(self, event):
        """Handle down arrow in suggestions listbox."""
        current_selection = self.suggestions_listbox.curselection()
        if current_selection:
            current_index = current_selection[0]
            if current_index == self.suggestions_listbox.size() - 1:
                # At bottom, stay at bottom
                return 'break'
            else:
                # Move down in the list
                new_index = current_index + 1
                self.suggestions_listbox.selection_clear(0, tk.END)
                self.suggestions_listbox.selection_set(new_index)
                self.suggestions_listbox.activate(new_index)
                self.suggestions_listbox.see(new_index)
                return 'break'

    def on_listbox_escape(self, event):
        """Handle escape key in suggestions listbox."""
        self.hide_suggestions()
        self.search_entry.focus_set()
        return 'break'

    def on_suggestion_click(self, event):
        """Handle single click on suggestion - select item and show popup."""
        index = self.suggestions_listbox.nearest(event.y)
        if 0 <= index < self.suggestions_listbox.size():
            self.suggestions_listbox.selection_clear(0, tk.END)
            self.suggestions_listbox.selection_set(index)
            self.suggestions_listbox.activate(index)

            # Show detailed info popup for selected signal
            suggestion_text = self.suggestions_listbox.get(index)
            # Extract signal name (before the first │ separator)
            signal_name = suggestion_text.split(' │')[0].strip()
            self.show_signal_info_popup(signal_name)

    def on_suggestion_double_click(self, event):
        """Handle double-click on suggestion - search immediately."""
        selection = self.suggestions_listbox.curselection()
        if selection:
            self.on_suggestion_select()

    def on_suggestion_return(self, event):
        """Handle Enter key in suggestions - search selected item."""
        self.on_suggestion_select()
        return 'break'

    def show_signal_info_popup(self, signal_name):
        """Show detailed signal information in a popup window."""
        signal_info = self.signal_index.get(signal_name.lower())
        if not signal_info:
            return

        # Close any existing popup first
        if hasattr(self, 'current_popup') and self.current_popup:
            try:
                if self.current_popup.winfo_exists():
                    self.current_popup.destroy()
            except tk.TclError:
                pass  # Popup already destroyed
            self.current_popup = None

        # Create popup window
        popup = tk.Toplevel(self.root)
        popup.title(f"Flash View - Signal Info: {signal_name}")
        popup.geometry("600x400")
        popup.transient(self.root)
        # Remove grab_set() to prevent blocking main window

        # Store reference to current popup
        self.current_popup = popup

        # Position popup relative to main window for better visibility
        popup.update_idletasks()

        # Get main window position and size
        main_x = self.root.winfo_x()
        main_y = self.root.winfo_y()
        main_width = self.root.winfo_width()
        main_height = self.root.winfo_height()

        # Position popup slightly offset from main window center
        popup_width = popup.winfo_reqwidth()
        popup_height = popup.winfo_reqheight()

        x = main_x + (main_width - popup_width) // 2 + 50  # Slight offset
        y = main_y + (main_height - popup_height) // 2 + 50  # Slight offset

        # Ensure popup stays on screen
        screen_width = popup.winfo_screenwidth()
        screen_height = popup.winfo_screenheight()

        if x + popup_width > screen_width:
            x = screen_width - popup_width - 20
        if y + popup_height > screen_height:
            y = screen_height - popup_height - 20
        if x < 0:
            x = 20
        if y < 0:
            y = 20

        popup.geometry(f"+{x}+{y}")

        # Bring popup to front more aggressively
        popup.lift()
        popup.attributes('-topmost', True)  # Force to front
        popup.update()  # Process the topmost change
        popup.attributes('-topmost', False)  # Remove topmost to allow normal behavior
        popup.focus_set()

        # Additional methods to ensure visibility
        popup.deiconify()  # Ensure not minimized
        popup.tkraise()  # Another method to bring to front

        # Bind ESC key to close popup with proper cleanup
        def close_popup():
            try:
                popup.destroy()
                self.current_popup = None
            except tk.TclError:
                pass

        popup.bind('<Escape>', lambda e: close_popup())
        popup.protocol("WM_DELETE_WINDOW", close_popup)  # Handle window close button

        # Main frame
        main_frame = ttk.Frame(popup, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Signal name and type
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 10))

        name_label = ttk.Label(header_frame, text=signal_name, font=('Arial', 14, 'bold'), foreground='blue')
        name_label.pack(side=tk.LEFT)

        type_label = ttk.Label(header_frame, text=f"({signal_info.get('full_type', 'Unknown')})",
                              font=('Arial', 12), foreground='green')
        type_label.pack(side=tk.LEFT, padx=(10, 0))

        # Description
        desc_frame = ttk.LabelFrame(main_frame, text="Description", padding="10")
        desc_frame.pack(fill=tk.X, pady=(0, 10))

        description = signal_info.get('description', 'No description available')
        desc_text = tk.Text(desc_frame, height=4, wrap=tk.WORD, font=('Arial', 10))
        desc_text.pack(fill=tk.BOTH, expand=True)
        desc_text.insert(1.0, description)
        desc_text.config(state=tk.DISABLED)

        # Additional info (if available from A2L search)
        info_frame = ttk.LabelFrame(main_frame, text="Additional Information", padding="10")
        info_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        info_text = tk.Text(info_frame, wrap=tk.WORD, font=('Consolas', 9))
        info_text.pack(fill=tk.BOTH, expand=True)

        # Try to get detailed info from A2L
        try:
            detailed_info = self.search_signal_in_a2l(signal_name, self.a2l_file_path.get())
            if detailed_info:
                info_content = f"Signal Name: {detailed_info['name']}\n"
                info_content += f"Type: {detailed_info['type']}\n"
                info_content += f"Description: {detailed_info.get('description', 'N/A')}\n\n"

                if detailed_info['type'] == 'MEASUREMENT':
                    if detailed_info.get('data_type'):
                        info_content += f"Data Type: {detailed_info['data_type']}\n"
                    if detailed_info.get('ecu_address'):
                        info_content += f"ECU Address: {detailed_info['ecu_address']}\n"

                elif detailed_info['type'] == 'CHARACTERISTIC':
                    if detailed_info.get('characteristic_type'):
                        info_content += f"Characteristic Type: {detailed_info['characteristic_type']}\n"
                    if detailed_info.get('address'):
                        info_content += f"Address: {detailed_info['address']}\n"

                if detailed_info.get('conversion'):
                    info_content += f"Conversion Method: {detailed_info['conversion']}\n"

                if detailed_info.get('lower_limit') and detailed_info.get('upper_limit'):
                    info_content += f"Range: {detailed_info['lower_limit']} to {detailed_info['upper_limit']}\n"

                info_text.insert(1.0, info_content)
            else:
                info_text.insert(1.0, "Detailed information not available.")
        except:
            info_text.insert(1.0, "Error loading detailed information.")

        info_text.config(state=tk.DISABLED)

        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        # Search button
        search_btn = ttk.Button(button_frame, text="Search This Signal",
                               command=lambda: self.search_from_popup(signal_name, popup))
        search_btn.pack(side=tk.LEFT)

        # Close button
        close_btn = ttk.Button(button_frame, text="Close", command=popup.destroy)
        close_btn.pack(side=tk.RIGHT)

        # Focus on popup
        popup.focus_set()

    def search_from_popup(self, signal_name, popup):
        """Search for signal from popup window."""
        self.search_signal_var.set(signal_name)
        popup.destroy()
        self.hide_suggestions()
        self.search_status_label.config(text="Searching...", foreground='blue')
        self.search_signal()

    def on_search_focus_out(self, event):
        """Handle focus out from search entry - delay hiding suggestions."""
        # Use after_idle to allow click events on suggestions to process first
        self.root.after_idle(self._check_focus_and_hide)

    def _check_focus_and_hide(self):
        """Check focus and hide suggestions if focus is not on search elements."""
        try:
            focused_widget = self.root.focus_get()
            # Don't hide if focus is on search entry or suggestions listbox
            if focused_widget not in [self.search_entry, self.suggestions_listbox]:
                self.hide_suggestions()
        except:
            # If there's any error getting focus, just hide suggestions
            self.hide_suggestions()

    def on_select_all_change(self):
        """Handle Select All checkbox changes."""
        select_all_state = self.filter_select_all.get()

        # Set all individual filters to match Select All state
        self.filter_measurement.set(select_all_state)
        self.filter_char_map.set(select_all_state)
        self.filter_char_val.set(select_all_state)
        self.filter_char_cur.set(select_all_state)
        self.filter_axis_pts.set(select_all_state)
        self.filter_other.set(select_all_state)

        # Refresh suggestions
        self.on_filter_change()

    def on_filter_change(self):
        """Handle filter checkbox changes."""
        # Update Select All checkbox based on individual filter states
        all_filters = [
            self.filter_measurement.get(),
            self.filter_char_map.get(),
            self.filter_char_val.get(),
            self.filter_char_cur.get(),
            self.filter_axis_pts.get(),
            self.filter_other.get()
        ]

        if all(all_filters):
            self.filter_select_all.set(True)
        elif not any(all_filters):
            self.filter_select_all.set(False)
        # If some are checked and some aren't, leave Select All as is

        # Refresh suggestions if they are currently visible
        if self.suggestions_frame.winfo_viewable():
            search_text = self.search_signal_var.get().strip()
            if len(search_text) >= 2:
                self.show_suggestions(search_text)

    def _is_signal_type_allowed(self, signal_type):
        """Check if a signal type is allowed by current filters."""
        if signal_type == 'MEASUREMENT':
            return self.filter_measurement.get()
        elif signal_type == 'CHAR_MAP':
            return self.filter_char_map.get()
        elif signal_type == 'CHAR_VAL':
            return self.filter_char_val.get()
        elif signal_type == 'CHAR_CUR':
            return self.filter_char_cur.get()
        elif signal_type == 'AXIS_PTS':
            return self.filter_axis_pts.get()
        else:
            return self.filter_other.get()

    def find_similar_signals(self, signal_name: str, max_results: int = 5) -> List[Tuple[str, float]]:
        """Find similar signals using fuzzy matching."""
        if not self.all_signals:
            return []

        # Use difflib to find close matches
        close_matches = difflib.get_close_matches(
            signal_name,
            self.all_signals,
            n=max_results,
            cutoff=0.3  # Lower cutoff for more lenient matching
        )

        # Calculate similarity ratios for ranking
        similar_signals = []
        for match in close_matches:
            ratio = difflib.SequenceMatcher(None, signal_name.lower(), match.lower()).ratio()
            signal_type = self.signal_index[match.lower()]['type']
            similar_signals.append((f"{match} ({signal_type})", ratio))

        # Sort by similarity ratio (highest first)
        similar_signals.sort(key=lambda x: x[1], reverse=True)

        return similar_signals

    def show_similar_signals_dialog(self, original_signal: str, similar_signals: List[Tuple[str, float]]):
        """Show a dialog with similar signals for user selection."""
        # Close any existing popup first
        if hasattr(self, 'current_popup') and self.current_popup:
            try:
                if self.current_popup.winfo_exists():
                    self.current_popup.destroy()
            except tk.TclError:
                pass
            self.current_popup = None

        dialog = tk.Toplevel(self.root)
        dialog.title("Flash View - Similar Signals Found")
        dialog.geometry("500x400")
        dialog.transient(self.root)
        # Remove grab_set() to prevent blocking

        # Store reference to current popup
        self.current_popup = dialog

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # Bring dialog to front more aggressively
        dialog.lift()
        dialog.attributes('-topmost', True)  # Force to front
        dialog.update()  # Process the topmost change
        dialog.attributes('-topmost', False)  # Remove topmost to allow normal behavior
        dialog.focus_set()

        # Additional methods to ensure visibility
        dialog.deiconify()  # Ensure not minimized
        dialog.tkraise()  # Another method to bring to front

        # Bind ESC key to close dialog with proper cleanup
        def close_dialog():
            try:
                dialog.destroy()
                self.current_popup = None
            except tk.TclError:
                pass

        dialog.bind('<Escape>', lambda e: close_dialog())
        dialog.protocol("WM_DELETE_WINDOW", close_dialog)

        # Main frame
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(
            main_frame,
            text=f"Signal '{original_signal}' not found.\nDid you mean one of these?",
            font=('Arial', 11, 'bold'),
            justify=tk.CENTER
        )
        title_label.pack(pady=(0, 15))

        # Listbox frame
        listbox_frame = ttk.Frame(main_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # Listbox with scrollbar
        listbox = tk.Listbox(listbox_frame, font=('Arial', 10), height=10)
        listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=listbox.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        listbox.configure(yscrollcommand=scrollbar.set)

        # Populate listbox
        for signal_info, ratio in similar_signals:
            similarity_percent = int(ratio * 100)
            listbox.insert(tk.END, f"{signal_info} - {similarity_percent}% match")

        # Select first item by default
        if similar_signals:
            listbox.selection_set(0)

        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        def on_select():
            selection = listbox.curselection()
            if selection:
                selected_text = listbox.get(selection[0])
                # Extract signal name (before the first space and parentheses)
                signal_name = selected_text.split(' (')[0]
                self.search_signal_var.set(signal_name)
                dialog.destroy()
                self.search_signal()

        def on_cancel():
            dialog.destroy()

        # Buttons
        select_button = ttk.Button(button_frame, text="Select", command=on_select)
        select_button.pack(side=tk.RIGHT, padx=(10, 0))

        cancel_button = ttk.Button(button_frame, text="Cancel", command=on_cancel)
        cancel_button.pack(side=tk.RIGHT)

        # Bind double-click and Enter key
        listbox.bind('<Double-Button-1>', lambda e: on_select())
        listbox.bind('<Return>', lambda e: on_select())

        # Focus on listbox
        listbox.focus_set()

    def search_signal_in_a2l(self, signal_name: str, file_path: str) -> Optional[Dict]:
        """Search for a specific signal in the A2L file efficiently."""
        signal_name_lower = signal_name.lower()

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                current_section = None
                current_signal = None
                signal_content = []
                in_target_signal = False

                for line in file:
                    line = line.strip()

                    # Check for beginning of any A2L object type
                    # Updated regex to handle signal names with double underscores and whitespace issues
                    a2l_object_match = re.match(r'/begin\s+(MEASUREMENT|CHARACTERISTIC|AXIS_PTS|COMPU_METHOD|COMPU_TAB|COMPU_VTAB|COMPU_VTAB_RANGE|GROUP|FUNCTION|MOD_PAR|MOD_COMMON)\s+([^\s]+)', line, re.IGNORECASE)

                    if a2l_object_match:
                        current_section = a2l_object_match.group(1).upper()  # MEASUREMENT, CHARACTERISTIC, AXIS_PTS, etc.
                        current_signal = a2l_object_match.group(2).strip()  # Strip whitespace
                        signal_content = []
                        in_target_signal = (current_signal.lower() == signal_name_lower)
                        continue

                    # Check for end of section
                    if re.match(r'/end\s+(MEASUREMENT|CHARACTERISTIC|AXIS_PTS|COMPU_METHOD|COMPU_TAB|COMPU_VTAB|COMPU_VTAB_RANGE|GROUP|FUNCTION|MOD_PAR|MOD_COMMON)', line, re.IGNORECASE):
                        if in_target_signal and current_signal:
                            # Found our target signal, extract information
                            content_str = '\n'.join(signal_content)
                            return self.extract_a2l_object_info(current_signal, current_section, content_str)

                        current_section = None
                        current_signal = None
                        signal_content = []
                        in_target_signal = False
                        continue

                    # Collect content if we're in the target signal
                    if in_target_signal:
                        signal_content.append(line)

            return None

        except Exception as e:
            raise Exception(f"Error reading file: {e}")

    def extract_measurement_info(self, signal_name: str, content: str) -> Dict:
        """Extract information from a MEASUREMENT block."""
        info = {
            'name': signal_name,
            'type': 'MEASUREMENT',
            'description': '',
            'data_type': '',
            'conversion': '',
            'lower_limit': '',
            'upper_limit': '',
            'display_identifier': '',
            'ecu_address': '',
            'raw_content': content.strip()
        }

        # Extract description (usually the first quoted string)
        desc_match = re.search(r'"([^"]*)"', content)
        if desc_match:
            info['description'] = desc_match.group(1)

        # Extract data type
        data_type_match = re.search(r'(UBYTE|SBYTE|UWORD|SWORD|ULONG|SLONG|FLOAT32_IEEE)', content)
        if data_type_match:
            info['data_type'] = data_type_match.group(1)

        # Extract common fields
        self.extract_common_fields(info, content)

        return info

    def extract_characteristic_info(self, signal_name: str, content: str) -> Dict:
        """Extract information from a CHARACTERISTIC block."""
        info = {
            'name': signal_name,
            'type': 'CHARACTERISTIC',
            'description': '',
            'characteristic_type': '',
            'address': '',
            'conversion': '',
            'lower_limit': '',
            'upper_limit': '',
            'display_identifier': '',
            'bit_mask': '',
            'raw_content': content.strip()
        }

        # Extract description (usually the first quoted string)
        desc_match = re.search(r'"([^"]*)"', content)
        if desc_match:
            info['description'] = desc_match.group(1)

        # Extract characteristic type
        char_type_match = re.search(r'(VALUE|CURVE|MAP|CUBOID|CUBE_4|CUBE_5)', content)
        if char_type_match:
            info['characteristic_type'] = char_type_match.group(1)

        # Extract address (hex value)
        addr_match = re.search(r'0x[0-9a-fA-F]+', content)
        if addr_match:
            info['address'] = addr_match.group(0)

        # Extract bit mask if present
        bit_mask_match = re.search(r'BIT_MASK\s+(0x[0-9a-fA-F]+)', content)
        if bit_mask_match:
            info['bit_mask'] = bit_mask_match.group(1)

        # Extract common fields
        self.extract_common_fields(info, content)

        return info

    def extract_a2l_object_info(self, signal_name: str, object_type: str, content: str) -> Dict:
        """Extract information from any A2L object type."""
        if object_type == 'MEASUREMENT':
            return self.extract_measurement_info(signal_name, content)
        elif object_type == 'CHARACTERISTIC':
            return self.extract_characteristic_info(signal_name, content)
        elif object_type == 'AXIS_PTS':
            return self.extract_axis_pts_info(signal_name, content)
        else:
            return self.extract_generic_a2l_info(signal_name, object_type, content)

    def extract_axis_pts_info(self, signal_name: str, content: str) -> Dict:
        """Extract information from an AXIS_PTS block."""
        info = {
            'name': signal_name,
            'type': 'AXIS_PTS',
            'description': '',
            'data_type': '',
            'address': '',
            'conversion': '',
            'lower_limit': '',
            'upper_limit': '',
            'display_identifier': '',
            'format': '',
            'raw_content': content.strip()
        }

        # Extract description (usually the first quoted string)
        desc_match = re.search(r'"([^"]*)"', content)
        if desc_match:
            info['description'] = desc_match.group(1)

        # Extract data type
        data_type_match = re.search(r'(UBYTE|SBYTE|UWORD|SWORD|ULONG|SLONG|FLOAT32_IEEE)', content)
        if data_type_match:
            info['data_type'] = data_type_match.group(1)

        # Extract address (hex value)
        addr_match = re.search(r'0x[0-9a-fA-F]+', content)
        if addr_match:
            info['address'] = addr_match.group(0)

        # Extract common fields
        self.extract_common_fields(info, content)

        return info

    def extract_generic_a2l_info(self, signal_name: str, object_type: str, content: str) -> Dict:
        """Extract information from any other A2L object type."""
        info = {
            'name': signal_name,
            'type': object_type,
            'description': '',
            'raw_content': content.strip()
        }

        # Extract description (usually the first quoted string)
        desc_match = re.search(r'"([^"]*)"', content)
        if desc_match:
            info['description'] = desc_match.group(1)

        # Extract common fields if present
        self.extract_common_fields(info, content)

        return info

    def extract_common_fields(self, info: Dict, content: str):
        """Extract common fields present in both MEASUREMENT and CHARACTERISTIC."""
        # Extract DISPLAY_IDENTIFIER
        # Updated regex to handle identifiers with double underscores
        display_id_match = re.search(r'DISPLAY_IDENTIFIER\s+([A-Za-z0-9_]+)', content)
        if display_id_match:
            info['display_identifier'] = display_id_match.group(1)

        # Extract ECU_ADDRESS
        ecu_addr_match = re.search(r'ECU_ADDRESS\s+(0x[0-9a-fA-F]+)', content)
        if ecu_addr_match:
            info['ecu_address'] = ecu_addr_match.group(1)

        # Extract conversion method
        conv_match = re.search(r'_CNV_[A-Z0-9_]+', content)
        if conv_match:
            info['conversion'] = conv_match.group(0)

        # Extract numeric values (limits)
        numbers = re.findall(r'\b\d+\.?\d*\b', content)
        if len(numbers) >= 3:
            info['lower_limit'] = numbers[2] if len(numbers) > 2 else ''
            info['upper_limit'] = numbers[3] if len(numbers) > 3 else ''

    def on_escape_key(self, event):
        """Handle ESC key press - close current popup if exists."""
        if hasattr(self, 'current_popup') and self.current_popup:
            try:
                if self.current_popup.winfo_exists():
                    self.current_popup.destroy()
                    self.current_popup = None
                    return 'break'  # Prevent further processing
            except tk.TclError:
                # Popup already destroyed
                self.current_popup = None
        return None  # Allow normal ESC processing if no popup

    def on_closing(self):
        """Handle application closing - cleanup clipboard monitoring."""
        self.stop_clipboard_monitoring()
        # Close any open popup
        if hasattr(self, 'current_popup') and self.current_popup:
            try:
                if self.current_popup.winfo_exists():
                    self.current_popup.destroy()
            except tk.TclError:
                pass
            self.current_popup = None
        self.root.destroy()


def main():
    """Main function to start the GUI application."""
    root = tk.Tk()
    app = A2LSignalSearchGUI(root)

    # Set window icon (if available)
    try:
        root.iconbitmap('icon.ico')  # Add an icon file if you have one
    except:
        pass  # Ignore if icon file not found

    # Start the GUI event loop
    root.mainloop()


if __name__ == "__main__":
    main()
