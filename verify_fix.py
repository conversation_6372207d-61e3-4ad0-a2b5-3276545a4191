#!/usr/bin/env python3
"""
Simple verification script for double underscore signal name fix
"""

import re

def test_regex_fix():
    """Test the regex fix for double underscore signal names."""
    print("Testing Regex Fix for Double Underscore Signal Names")
    print("=" * 55)
    
    # The problematic signal name
    test_signal = "LDP_TCO__IP_IGA_ST"
    test_line = f"/begin MEASUREMENT {test_signal}"
    
    print(f"Testing signal name: {test_signal}")
    print(f"Test line: {test_line}")
    print()
    
    # Old pattern (problematic)
    old_pattern = r'/begin\s+MEASUREMENT\s+(\w+)'
    print("Old pattern: r'/begin\\s+MEASUREMENT\\s+(\\w+)'")
    old_match = re.match(old_pattern, test_line, re.IGNORECASE)
    if old_match:
        print(f"  ✓ Old pattern matched: '{old_match.group(1)}'")
    else:
        print("  ✗ Old pattern failed to match")
    
    print()
    
    # New pattern (fixed)
    new_pattern = r'/begin\s+MEASUREMENT\s+([A-Za-z0-9_]+)'
    print("New pattern: r'/begin\\s+MEASUREMENT\\s+([A-Za-z0-9_]+)'")
    new_match = re.match(new_pattern, test_line, re.IGNORECASE)
    if new_match:
        print(f"  ✓ New pattern matched: '{new_match.group(1)}'")
    else:
        print("  ✗ New pattern failed to match")
    
    print()
    
    # Test other signal names to ensure compatibility
    other_signals = [
        "normal_signal",
        "signal_with_single_underscore",
        "SIGNAL_WITH_CAPS",
        "signal123",
        "A_B__C___D"  # Multiple underscores
    ]
    
    print("Testing compatibility with other signal names:")
    print("-" * 40)
    
    for signal in other_signals:
        test_line = f"/begin MEASUREMENT {signal}"
        match = re.match(new_pattern, test_line, re.IGNORECASE)
        if match:
            print(f"  ✓ {signal}")
        else:
            print(f"  ✗ {signal}")
    
    print()
    print("=" * 55)
    print("CONCLUSION:")
    print("The new regex pattern ([A-Za-z0-9_]+) should correctly")
    print("match signal names with double underscores like:")
    print("LDP_TCO__IP_IGA_ST")
    print("=" * 55)

if __name__ == "__main__":
    test_regex_fix()
