#!/usr/bin/env python3
"""
A2L Signal Search Web Application

A Flask web application for searching signals in A2L files.
Features:
- File upload for A2L files
- Signal search with case-insensitive matching
- Description display with expandable details
- Clean, responsive UI
"""

import os
import re
import tempfile
from flask import Flask, render_template, request, jsonify, session
from werkzeug.utils import secure_filename
from typing import Dict, Optional

app = Flask(__name__)
app.secret_key = 'a2l_search_secret_key_change_in_production'
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size

# Configure upload folder
UPLOAD_FOLDER = 'uploads'
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

ALLOWED_EXTENSIONS = {'a2l', 'A2L'}

def allowed_file(filename):
    """Check if file has allowed extension."""
    return '.' in filename and filename.rsplit('.', 1)[1] in ALLOWED_EXTENSIONS

def search_signal_in_a2l_web(signal_name: str, file_path: str) -> Optional[Dict]:
    """Search for a specific signal in the A2L file efficiently for web use."""
    signal_name_lower = signal_name.lower()
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            current_section = None
            current_signal = None
            signal_content = []
            in_target_signal = False
            
            for line in file:
                line = line.strip()
                
                # Check for beginning of MEASUREMENT or CHARACTERISTIC
                # Updated regex to handle signal names with double underscores and other valid characters
                measurement_match = re.match(r'/begin\s+MEASUREMENT\s+([A-Za-z0-9_]+)', line, re.IGNORECASE)
                characteristic_match = re.match(r'/begin\s+CHARACTERISTIC\s+([A-Za-z0-9_]+)', line, re.IGNORECASE)
                
                if measurement_match:
                    current_section = 'MEASUREMENT'
                    current_signal = measurement_match.group(1)
                    signal_content = []
                    in_target_signal = (current_signal.lower() == signal_name_lower)
                    continue
                
                elif characteristic_match:
                    current_section = 'CHARACTERISTIC'
                    current_signal = characteristic_match.group(1)
                    signal_content = []
                    in_target_signal = (current_signal.lower() == signal_name_lower)
                    continue
                
                # Check for end of section
                if re.match(r'/end\s+(MEASUREMENT|CHARACTERISTIC)', line, re.IGNORECASE):
                    if in_target_signal and current_signal:
                        # Found our target signal, extract information
                        content_str = '\n'.join(signal_content)
                        if current_section == 'MEASUREMENT':
                            return extract_measurement_info_web(current_signal, content_str)
                        else:
                            return extract_characteristic_info_web(current_signal, content_str)
                    
                    current_section = None
                    current_signal = None
                    signal_content = []
                    in_target_signal = False
                    continue
                
                # Collect content if we're in the target signal
                if in_target_signal:
                    signal_content.append(line)
        
        return None
        
    except Exception as e:
        print(f"Error reading file: {e}")
        return None

def extract_measurement_info_web(signal_name: str, content: str) -> Dict:
    """Extract information from a MEASUREMENT block for web display."""
    info = {
        'name': signal_name,
        'type': 'MEASUREMENT',
        'description': '',
        'data_type': '',
        'conversion': '',
        'lower_limit': '',
        'upper_limit': '',
        'display_identifier': '',
        'ecu_address': '',
        'raw_content': content.strip()
    }
    
    # Extract description (usually the first quoted string)
    desc_match = re.search(r'"([^"]*)"', content)
    if desc_match:
        info['description'] = desc_match.group(1)
    
    # Extract data type
    data_type_match = re.search(r'(UBYTE|SBYTE|UWORD|SWORD|ULONG|SLONG|FLOAT32_IEEE)', content)
    if data_type_match:
        info['data_type'] = data_type_match.group(1)
    
    # Extract common fields
    extract_common_fields_web(info, content)
    
    return info

def extract_characteristic_info_web(signal_name: str, content: str) -> Dict:
    """Extract information from a CHARACTERISTIC block for web display."""
    info = {
        'name': signal_name,
        'type': 'CHARACTERISTIC',
        'description': '',
        'characteristic_type': '',
        'address': '',
        'conversion': '',
        'lower_limit': '',
        'upper_limit': '',
        'display_identifier': '',
        'bit_mask': '',
        'raw_content': content.strip()
    }
    
    # Extract description (usually the first quoted string)
    desc_match = re.search(r'"([^"]*)"', content)
    if desc_match:
        info['description'] = desc_match.group(1)
    
    # Extract characteristic type
    char_type_match = re.search(r'(VALUE|CURVE|MAP|CUBOID|CUBE_4|CUBE_5)', content)
    if char_type_match:
        info['characteristic_type'] = char_type_match.group(1)
    
    # Extract address (hex value)
    addr_match = re.search(r'0x[0-9a-fA-F]+', content)
    if addr_match:
        info['address'] = addr_match.group(0)
    
    # Extract bit mask if present
    bit_mask_match = re.search(r'BIT_MASK\s+(0x[0-9a-fA-F]+)', content)
    if bit_mask_match:
        info['bit_mask'] = bit_mask_match.group(1)
    
    # Extract common fields
    extract_common_fields_web(info, content)
    
    return info

def extract_common_fields_web(info: Dict, content: str):
    """Extract common fields present in both MEASUREMENT and CHARACTERISTIC for web display."""
    # Extract DISPLAY_IDENTIFIER
    # Updated regex to handle identifiers with double underscores
    display_id_match = re.search(r'DISPLAY_IDENTIFIER\s+([A-Za-z0-9_]+)', content)
    if display_id_match:
        info['display_identifier'] = display_id_match.group(1)
    
    # Extract ECU_ADDRESS
    ecu_addr_match = re.search(r'ECU_ADDRESS\s+(0x[0-9a-fA-F]+)', content)
    if ecu_addr_match:
        info['ecu_address'] = ecu_addr_match.group(1)
    
    # Extract conversion method
    conv_match = re.search(r'_CNV_[A-Z0-9_]+', content)
    if conv_match:
        info['conversion'] = conv_match.group(0)
    
    # Extract numeric values (limits)
    numbers = re.findall(r'\b\d+\.?\d*\b', content)
    if len(numbers) >= 3:
        info['lower_limit'] = numbers[2] if len(numbers) > 2 else ''
        info['upper_limit'] = numbers[3] if len(numbers) > 3 else ''

@app.route('/')
def index():
    """Main page with file upload and search interface."""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle A2L file upload."""
    if 'file' not in request.files:
        return jsonify({'error': 'No file selected'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # Store filepath in session
        session['a2l_file'] = filepath
        session['filename'] = filename
        
        return jsonify({
            'success': True, 
            'message': f'File "{filename}" uploaded successfully',
            'filename': filename
        })
    
    return jsonify({'error': 'Invalid file type. Please upload an A2L file.'}), 400

@app.route('/search', methods=['POST'])
def search_signal():
    """Search for a signal in the uploaded A2L file."""
    data = request.get_json()
    signal_name = data.get('signal_name', '').strip()
    
    if not signal_name:
        return jsonify({'error': 'Please enter a signal name'}), 400
    
    if 'a2l_file' not in session:
        return jsonify({'error': 'Please upload an A2L file first'}), 400
    
    a2l_file = session['a2l_file']
    if not os.path.exists(a2l_file):
        return jsonify({'error': 'Uploaded file not found. Please upload again.'}), 400
    
    # Search for the signal
    signal_info = search_signal_in_a2l_web(signal_name, a2l_file)
    
    if signal_info:
        return jsonify({
            'found': True,
            'signal': {
                'name': signal_info['name'],
                'description': signal_info['description'],
                'type': signal_info['type'],
                'details': signal_info  # Full details for "More Info"
            }
        })
    else:
        return jsonify({
            'found': False,
            'message': f'Signal "{signal_name}" not found in the A2L file.'
        })

@app.route('/clear', methods=['POST'])
def clear_session():
    """Clear the uploaded file from session."""
    if 'a2l_file' in session:
        filepath = session['a2l_file']
        if os.path.exists(filepath):
            os.remove(filepath)
        session.pop('a2l_file', None)
        session.pop('filename', None)
    
    return jsonify({'success': True, 'message': 'Session cleared'})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
